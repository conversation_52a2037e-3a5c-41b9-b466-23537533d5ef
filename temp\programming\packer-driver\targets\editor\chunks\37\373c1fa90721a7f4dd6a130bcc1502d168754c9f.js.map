{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts"], "names": ["_decorator", "Label", "ProgressBar", "tween", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "StatisticsUI", "ccclass", "property", "SettlementUI", "_scoreTween", "_expTween", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "btnNext", "onNextClick", "scoreAdd", "string", "lblScore", "scoreHigh", "gap", "score", "value", "to", "onUpdate", "target", "undefined", "Math", "round", "toString", "start", "exp", "finalExp", "expProBar", "progress", "lblExp", "lblLevel", "stop", "closeUI", "openUI", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AAChCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;8BAIjBY,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACT,WAAD,C,UAERS,QAAQ,CAACV,KAAD,C,2BAnBb,MACaW,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAqB7BC,WArB6B;AAqBX;AArBW,eAsB7BC,SAtB6B;AAAA;;AAsBX;AAEN,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACA,eAAKC,OAAL,CAAcF,QAAd,CAAuB,KAAKG,WAA5B,EAAyC,IAAzC;AAEA,eAAKC,QAAL,CAAeC,MAAf,GAAwB,UAAU,GAAV,GAAgB,GAAxC;AACA,eAAKC,QAAL,CAAeD,MAAf,GAAwB,GAAxB;AACA,eAAKE,SAAL,CAAgBF,MAAhB,GAAyB,WAAW,KAApC;AAEA,gBAAMG,GAAG,GAAG,GAAZ,CARqB,CASrB;;AACA,gBAAMC,KAAK,GAAG,IAAd;AACA,eAAKpB,WAAL,GAAmBV,KAAK,CAAC;AAAE+B,YAAAA,KAAK,EAAE;AAAT,WAAD,CAAL,CACdC,EADc,CACXH,GADW,EACN;AAAEE,YAAAA,KAAK,EAAED;AAAT,WADM,EACY;AACvBG,YAAAA,QAAQ,EAAGC,MAAD,IAAY;AAClB,kBAAI,CAAC,KAAKP,QAAN,IAAkBO,MAAM,KAAKC,SAAjC,EAA4C;AAC5C,mBAAKR,QAAL,CAAcD,MAAd,GAAuBU,IAAI,CAACC,KAAL,CAAWH,MAAM,CAACH,KAAlB,EAAyBO,QAAzB,EAAvB;AACH;AAJsB,WADZ,EAOdC,KAPc,EAAnB;AASA,gBAAMC,GAAG,GAAG,GAAZ;AACA,gBAAMC,QAAQ,GAAG,KAAjB,CArBqB,CAqBG;AACxB;;AACA,eAAK9B,SAAL,GAAiBX,KAAK,CAAC,KAAK0C,SAAN,CAAL,CACZV,EADY,CACTH,GADS,EACJ;AAAEc,YAAAA,QAAQ,EAAEH;AAAZ,WADI,EACe;AACxBP,YAAAA,QAAQ,EAAE,MAAM;AACZ,kBAAI,CAAC,KAAKS,SAAV,EAAqB;AACrB,mBAAKE,MAAL,CAAalB,MAAb,GAAuB,GAAEU,IAAI,CAACC,KAAL,CAAWI,QAAQ,GAAG,KAAKC,SAAL,CAAeC,QAArC,CAA+C,IAAGF,QAAS,EAApF;AACH;AAJuB,WADf,EAOZF,KAPY,EAAjB;AASA,eAAKM,QAAL,CAAenB,MAAf,GAAwB,OAAO,EAA/B;AAEH;;AACc,cAATJ,SAAS,GAAG;AACd;AACA,cAAI,KAAKZ,WAAT,EAAsB,KAAKA,WAAL,CAAiBoC,IAAjB;AACtB,cAAI,KAAKnC,SAAT,EAAoB,KAAKA,SAAL,CAAemC,IAAf;AACpB;AAAA;AAAA,8BAAMC,OAAN,CAActC,YAAd;AACH;;AACgB,cAAXe,WAAW,GAAG;AAChB;AACA,cAAI,KAAKd,WAAT,EAAsB,KAAKA,WAAL,CAAiBoC,IAAjB;AACtB,cAAI,KAAKnC,SAAT,EAAoB,KAAKA,SAAL,CAAemC,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAME,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMD,OAAN,CAActC,YAAd;AACH;;AAEW,cAANwC,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB;AAC3B;AACA,cAAI,KAAKzC,WAAT,EAAsB,KAAKA,WAAL,CAAiBoC,IAAjB;AACtB,cAAI,KAAKnC,SAAT,EAAoB,KAAKA,SAAL,CAAemC,IAAf;AACvB;;AAzFoC,O;;;;;iBAGV,I;;;;;;;iBAEE,I;;;;;;;iBAGJ,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAGD,I;;;;;;;iBAEO,I;;;;;;;iBAET,I", "sourcesContent": ["import { _decorator, Label, ProgressBar, tween } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport { StatisticsUI } from './StatisticsUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('SettlementUI')\r\nexport class SettlementUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Label)\r\n    scoreAdd: Label | null = null;\r\n    @property(Label)\r\n    scoreHigh: Label | null = null;\r\n\r\n    @property(Label)\r\n    lblLevel: Label | null = null;\r\n    @property(ProgressBar)\r\n    expProBar: ProgressBar | null = null;\r\n    @property(Label)\r\n    lblExp: Label | null = null;\r\n\r\n    private _scoreTween: any; // 分数动画的 tween 引用\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    public static getUrl(): string { return \"prefab/ui/SettlementUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n\r\n        this.scoreAdd!.string = \"分数加成 \" + 123 + \"%\";\r\n        this.lblScore!.string = \"0\";\r\n        this.scoreHigh!.string = \"历史最高分 \" + 10000;\r\n\r\n        const gap = 0.5;\r\n        // 分数动画\r\n        const score = 1000;\r\n        this._scoreTween = tween({ value: 0 })\r\n            .to(gap, { value: score }, {\r\n                onUpdate: (target) => {\r\n                    if (!this.lblScore || target === undefined) return;\r\n                    this.lblScore.string = Math.round(target.value).toString();\r\n                }\r\n            })\r\n            .start();\r\n\r\n        const exp = 0.8;\r\n        const finalExp = 10000; // 最终值\r\n        // 经验条动画\r\n        this._expTween = tween(this.expProBar!)\r\n            .to(gap, { progress: exp }, {\r\n                onUpdate: () => {\r\n                    if (!this.expProBar) return;\r\n                    this.lblExp!.string = `${Math.round(finalExp * this.expProBar.progress)}/${finalExp}`;\r\n                }\r\n            })\r\n            .start();\r\n\r\n        this.lblLevel!.string = \"lv\" + 10;\r\n\r\n    }\r\n    async onOKClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n    async onNextClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n    }\r\n}\r\n"]}