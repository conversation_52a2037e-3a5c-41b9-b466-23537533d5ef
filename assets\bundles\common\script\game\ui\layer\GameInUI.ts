import { _decorator, Component, Node } from 'cc';
import EventManager from '../../../event/EventManager';
import { GameEvent } from '../../event/GameEvent';
import { GameIns } from '../../GameIns';
import { UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { GamePauseUI } from '../../../ui/gameui/game/GamePauseUI';
const { ccclass, property } = _decorator;

@ccclass('GameInUI')
export class GameInUI extends Component {

    @property(Node)
    tipNode: Node | null = null
    @property(Node)
    btnPause: Node | null = null

    protected onEnable(): void {
        this.tipNode!.active = true;
        this.btnPause!.active = false;
        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);
    }

    protected onDisable(): void {
        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);
    }

    onEventGameStart() {
        this.tipNode!.active = false;
    }


    setTouchState(isTouch: boolean) {
        if (this.btnPause){
            this.btnPause.active = !isTouch;
        }
    }

    onBtnPauseClicked() {
        GameIns.gameRuleManager.gamePause();
        UIMgr.openUI(GamePauseUI);
    }
}


