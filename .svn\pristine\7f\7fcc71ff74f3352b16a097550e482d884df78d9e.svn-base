import { _decorator, assetManager, Component, instantiate, Prefab } from 'cc';
import { EDITOR } from 'cc/env';
import { LevelDataEmittier } from '../../leveldata/leveldata';
const { ccclass, executeInEditMode } = _decorator;

@ccclass('EmittierTerrain')
@executeInEditMode()
export class EmittierTerrain extends Component {

    private _emittier: LevelDataEmittier | null = null;
    private _bStart = false;
    private _terrainPrefab: Prefab[] = [];

    protected onLoad(): void {
        if (EDITOR) {
            this.node.removeAllChildren();
        }
    }

    public initEmittier(emittier: LevelDataEmittier): void {
        this.node.removeAllChildren();
        this._emittier = emittier;
        if (EDITOR) {
            assetManager.loadAny({uuid:emittier.uuid}, (err: Error, prefab:Prefab) => {
                if (err) {
                    console.error("EmittierTerrain initEmittier load prefab err", err);
                    return
                } 
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(emittier.position.x, emittier.position.y, 0);
                terrainNode.setScale(emittier.scale.x, emittier.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, emittier.rotation);
                this.node.addChild(terrainNode);                
            });
        } else {

        }
    }

    public tick(dt: number): void {
        if (!this._bStart) return;

        
    }

    protected update(): void {
        
    }

    public play(bPlay: boolean): void {
        if (EDITOR) {
            if (bPlay) {
            }
        }
    }

    protected onDestroy(): void {
        this.node.removeAllChildren();
    }

    private _loadElems(): void {
        
    }
}

