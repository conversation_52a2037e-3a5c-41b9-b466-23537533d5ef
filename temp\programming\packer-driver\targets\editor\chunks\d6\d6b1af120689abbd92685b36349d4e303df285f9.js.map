{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GameReviveUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "GameIns", "ccclass", "property", "GameReviveUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getUIOption", "isClickBgCloseUI", "onLoad", "closeUI", "onShow", "onHide", "onClose", "onDestroy", "onBtnReviveClicked", "battleManager", "relifeBattle", "onBtnAdClicked"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;8BAGjBO,Y,WADZF,OAAO,CAAC,cAAD,C,gBAAR,MACaE,YADb;AAAA;AAAA,4BACyC;AAEjB,eAANC,MAAM,GAAW;AAAE,iBAAO,sBAAP;AAAgC;;AAC3C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACnC,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAE9DC,QAAAA,MAAM,GAAS,CAExB;;AACY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcP,YAAd;AACH;;AACW,cAANQ,MAAM,GAAkB,CAC7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA,eAAKP,OAAL;AACH;;AAEDQ,QAAAA,cAAc,GAAG;AACb,eAAKR,OAAL;AACA;AAAA;AAAA,kCAAQM,aAAR,CAAsBC,YAAtB;AACH;;AA7BoC,O", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\n\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GameIns } from '../../../game/GameIns';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameReviveUI')\r\nexport class GameReviveUI extends BaseUI {\r\n\r\n    public static getUrl(): string { return \"ui/game/GameReviveUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(GameReviveUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    onBtnReviveClicked() {\r\n        GameIns.battleManager.relifeBattle();\r\n        this.closeUI();\r\n    }\r\n\r\n    onBtnAdClicked() {\r\n        this.closeUI();\r\n        GameIns.battleManager.relifeBattle();\r\n    }\r\n}\r\n\r\n\r\n"]}