{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts"], "names": ["_decorator", "Node", "RichText", "UITransform", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "ccclass", "property", "AnnouncementUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "closeUI", "onShow", "messgae", "wid", "content", "getComponent", "width", "hei", "richText", "height", "setContentSize", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;;AAC5BC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;gCAGjBW,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACR,QAAD,C,2BARb,MACaS,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAUnB,eAANC,MAAM,GAAW;AAAE,iBAAO,0BAAP;AAAoC;;AAC/C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACH;;AACc,cAATA,SAAS,GAAG;AACd;AAAA;AAAA,8BAAMC,OAAN,CAAcZ,cAAd;AACH;;AAEW,cAANa,MAAM,CAACC,OAAD,EAAiC;AACzC,cAAIC,GAAG,GAAG,KAAKC,OAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAyC0B,KAAnD;AACA,cAAIC,GAAG,GAAG,KAAKC,QAAL,CAAeH,YAAf,CAA4BzB,WAA5B,EAA0C6B,MAApD;AACAF,UAAAA,GAAG,GAAGA,GAAG,GAAG,GAAN,GAAY,GAAZ,GAAkBA,GAAxB;AACA,eAAKH,OAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAyC8B,cAAzC,CAAwDP,GAAxD,EAA6DI,GAA7D;AACH;;AACW,cAANI,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAE9B;;AAlCsC,O;;;;;iBAEhB,I;;;;;;;iBAGI,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Node, RichText, UITransform, WebView } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('AnnouncementUI')\r\nexport class AnnouncementUI extends BaseUI {\r\n    @property(Node)\r\n    content: Node | null = null;\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n\r\n    @property(RichText)\r\n    richText: RichText | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/AnnouncementUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n    }\r\n    async onOKClick() {\r\n        UIMgr.closeUI(AnnouncementUI);\r\n    }\r\n\r\n    async onShow(messgae: string): Promise<void> {\r\n        let wid = this.content!.getComponent(UITransform)!.width;\r\n        let hei = this.richText!.getComponent(UITransform)!.height;\r\n        hei = hei < 800 ? 800 : hei;\r\n        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n}\r\n"]}