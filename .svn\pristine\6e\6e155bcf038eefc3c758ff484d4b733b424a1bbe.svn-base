import { _decorator, Component, instantiate, Node, Prefab, view } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { LevelDataEvent, LevelDataLayer, LevelDataWave } from "../../../leveldata/leveldata";
import { LevelDataEventTriggerType } from "../../../leveldata/trigger/LevelDataEventTrigger";
import { LevelDataEventTriggerLog } from "../../../leveldata/trigger/LevelDataEventTriggerLog";
import { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from "../../../leveldata/trigger/LevelDataEventTriggerWave";
import { GameIns } from "../../GameIns";
import { Wave } from "../../wave/Wave";
import { LevelDataEventCondtion } from "../../../leveldata/condition/LevelDataEventCondtion";

const { ccclass } = _decorator;

const TerrainsNodeName = "terrains";
const DynamicNodeName = "dynamic";
const WaveNodeName = "waves";
const EventNodeName = "events"

@ccclass('LevelLayerUI')
export class LevelLayerUI extends Component {
    public backgrounds: Prefab[] = [];
    private _offSetY: number = 0; // 当前关卡的偏移量
    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）

    private terrainsNode: Node | null = null;
    private dynamicNode: Node | null = null;
    // private waves: LevelDataWave[] = [];
    private events: LevelDataEvent[] = [];
    private enableEvents: LevelDataEvent[] = [];

    public get TrackBackground(): boolean {
        return this._bTrackBackground;
    }
    public set TrackBackground(value: boolean) {
        this._bTrackBackground = value;
    }

    onLoad(): void {

    }

    public initByLevelData(data: LevelDataLayer, offSetY: number): void {
        this._offSetY = offSetY;
        this.node.setPosition(0, offSetY, 0);

        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);

        console.log('LevelLayerUI', " initByLevelData");
        this.backgrounds = [];

        data.terrains?.forEach((terrain) => {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)
            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                if (err) {
                    console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                    return;
                }
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);
            });
        });
        // this.waves = [...data.waves]
        // this.waves.sort((a, b) => a.position.y - b.position.y);
        this.events = [...data.events]
        this.events.sort((a, b) => a.position.y - b.position.y);
    }

    public tick(deltaTime: number, speed: number): void {
        if (this.TrackBackground === true) {
            const posY = this.node.getPosition().y;
            const topPosY = view.getVisibleSize().height / 2;
            if (posY < topPosY) {
                this._bTrackBackground = false;
            }
        }
        const prePosY = this.node.getPosition().y;
        this.node.setPosition(0, prePosY - deltaTime * speed, 0);
        // while (this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {
            // const wave = this.waves[0];
            // this.waves.shift();
            // const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)
            // MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
            //     if (err) {
            //         console.error('LevelLayerUI', " tick load wave prefab err", err);
            //         return;
            //     }
            //     const waveComp = instantiate(prefab).getComponent(Wave)
            //     GameIns.waveManager.addWaveByLevel(waveComp!, wave.position.x, wave.position.y - this.node.position.y);
            // });
        // }
        while (this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {
            const event = this.events[0];
            this.events.shift();
            this.enableEvents.push(event);
        }
        for (let i = this.enableEvents.length - 1; i >= 0; i--) {
            const event = this.enableEvents[i];
            let condResult = this.evalConditions(event.conditions);

            if (condResult) {
                this.enableEvents.splice(i, 1);
                this.execActions(event);
            }
        }
    }

    private _getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    evalConditions(conditions: LevelDataEventCondtion[]): boolean {
        let result = true;
        for (let cond of conditions) {
            switch (cond._type) {
            }
        }
        return result;
    }

    execActions(event: LevelDataEvent): void {
        for (let trigger of event.triggers) {
            switch (trigger._type) {
                case LevelDataEventTriggerType.Log:
                    console.log("LevelLayerUI", "trigger log", (trigger as LevelDataEventTriggerLog).message);
                    break;
                case LevelDataEventTriggerType.Audio:
                    break;
                case LevelDataEventTriggerType.Wave:
                    console.log("LevelLayerUI", "trigger wave");
                    const waveTrigger = trigger as LevelDataEventTriggerWave;
                    if (!waveTrigger.waveGroup || waveTrigger.waveGroup.length == 0) {
                        break;
                    }

                    let totalWeight = 0;
                    waveTrigger.waveGroup.forEach((waveGroup) => {
                        totalWeight += waveGroup.weight;
                    });
                    let randomWeight = Math.floor(Math.random() * totalWeight);
                    let curWeight = 0;
                    let selectedWaveGroup: LevelDataEventWaveGroup | null = null;
                    for (let waveGroup of waveTrigger.waveGroup) {
                        curWeight += waveGroup.weight;
                        if (randomWeight <= curWeight) {
                            selectedWaveGroup = waveGroup;
                            break;
                        }
                    }

                    if (selectedWaveGroup == null || !selectedWaveGroup.waveUUID || selectedWaveGroup.waveUUID.length == 0) {
                        break;
                    }

                    selectedWaveGroup.waveUUID.forEach((waveUUID) => {
                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)
                        MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                            if (err) {
                                console.error('LevelLayerUI', " tick load wave prefab err", err);
                                return;
                            }
                            const waveComp = instantiate(prefab).getComponent(Wave)!
                            GameIns.waveManager.addWaveByLevel(waveComp, event.position.x, Math.max(0, event.position.y - this.node.position.y));
                        });
                    });
                    break;
                default:
                    break;
            }
        }
    }
}