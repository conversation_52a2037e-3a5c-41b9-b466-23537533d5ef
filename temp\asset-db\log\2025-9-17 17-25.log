2025-9-17 17:25:08-log: Cannot access game frame or container.
2025-9-17 17:25:08-debug: asset-db:require-engine-code (402ms)
2025-9-17 17:25:08-log: meshopt wasm decoder initialized
2025-9-17 17:25:08-log: [box2d]:box2d wasm lib loaded.
2025-9-17 17:25:08-log: [bullet]:bullet wasm lib loaded.
2025-9-17 17:25:08-log: Cocos Creator v3.8.6
2025-9-17 17:25:08-log: Using legacy pipeline
2025-9-17 17:25:08-log: Forward render pipeline initialized.
2025-9-17 17:25:08-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.85MB, end 79.89MB, increase: 49.05MB
2025-9-17 17:25:09-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.24MB, end 288.84MB, increase: 204.60MB
2025-9-17 17:25:09-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.65MB, end 287.30MB, increase: 206.65MB
2025-9-17 17:25:08-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.81MB, end 84.20MB, increase: 3.39MB
2025-9-17 17:25:09-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.91MB, end 287.33MB, increase: 207.42MB
2025-9-17 17:25:09-debug: run package(google-play) handler(enable) start
2025-9-17 17:25:09-debug: run package(google-play) handler(enable) success!
2025-9-17 17:25:09-debug: run package(harmonyos-next) handler(enable) success!
2025-9-17 17:25:09-debug: run package(honor-mini-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(harmonyos-next) handler(enable) start
2025-9-17 17:25:09-debug: run package(huawei-agc) handler(enable) start
2025-9-17 17:25:09-debug: run package(huawei-quick-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(huawei-agc) handler(enable) success!
2025-9-17 17:25:09-debug: run package(honor-mini-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(ios) handler(enable) start
2025-9-17 17:25:09-debug: run package(ios) handler(enable) success!
2025-9-17 17:25:09-debug: run package(linux) handler(enable) start
2025-9-17 17:25:09-debug: run package(migu-mini-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(mac) handler(enable) start
2025-9-17 17:25:09-debug: run package(linux) handler(enable) success!
2025-9-17 17:25:09-debug: run package(migu-mini-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(mac) handler(enable) success!
2025-9-17 17:25:09-debug: run package(native) handler(enable) success!
2025-9-17 17:25:09-debug: run package(native) handler(enable) start
2025-9-17 17:25:09-debug: run package(ohos) handler(enable) start
2025-9-17 17:25:09-debug: run package(oppo-mini-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-17 17:25:09-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-17 17:25:09-debug: run package(ohos) handler(enable) success!
2025-9-17 17:25:09-debug: run package(taobao-mini-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(vivo-mini-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(web-desktop) handler(enable) start
2025-9-17 17:25:09-debug: run package(web-mobile) handler(enable) start
2025-9-17 17:25:09-debug: run package(web-mobile) handler(enable) success!
2025-9-17 17:25:09-debug: run package(wechatgame) handler(enable) start
2025-9-17 17:25:09-debug: run package(wechatgame) handler(enable) success!
2025-9-17 17:25:09-debug: run package(wechatprogram) handler(enable) start
2025-9-17 17:25:09-debug: run package(web-desktop) handler(enable) success!
2025-9-17 17:25:09-debug: run package(wechatprogram) handler(enable) success!
2025-9-17 17:25:09-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-17 17:25:09-debug: run package(windows) handler(enable) start
2025-9-17 17:25:09-debug: run package(windows) handler(enable) success!
2025-9-17 17:25:09-debug: run package(cocos-service) handler(enable) success!
2025-9-17 17:25:09-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-17 17:25:09-debug: run package(cocos-service) handler(enable) start
2025-9-17 17:25:09-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-17 17:25:09-debug: run package(im-plugin) handler(enable) start
2025-9-17 17:25:09-debug: run package(im-plugin) handler(enable) success!
2025-9-17 17:25:09-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-17 17:25:09-debug: asset-db:worker-init: initPlugin (1088ms)
2025-9-17 17:25:09-debug: [Assets Memory track]: asset-db:worker-init start:30.84MB, end 289.53MB, increase: 258.69MB
2025-9-17 17:25:09-debug: Run asset db hook programming:beforePreStart ...
2025-9-17 17:25:09-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-17 17:25:09-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-17 17:25:09-debug: Run asset db hook programming:beforePreStart success!
2025-9-17 17:25:09-debug: run package(emitter-editor) handler(enable) success!
2025-9-17 17:25:09-debug: run package(emitter-editor) handler(enable) start
2025-9-17 17:25:09-debug: run package(level-editor) handler(enable) start
2025-9-17 17:25:09-debug: run package(level-editor) handler(enable) success!
2025-9-17 17:25:09-debug: Preimport db internal success
2025-9-17 17:25:09-debug: run package(localization-editor) handler(enable) success!
2025-9-17 17:25:09-debug: run package(localization-editor) handler(enable) start
2025-9-17 17:25:09-debug: asset-db:worker-init (1728ms)
2025-9-17 17:25:09-debug: asset-db-hook-programming-beforePreStart (145ms)
2025-9-17 17:25:09-debug: asset-db-hook-engine-extends-beforePreStart (144ms)
2025-9-17 17:25:09-debug: Preimport db assets success
2025-9-17 17:25:09-debug: Run asset db hook programming:afterPreStart ...
2025-9-17 17:25:09-debug: starting packer-driver...
2025-9-17 17:25:09-debug: run package(wave-editor) handler(enable) start
2025-9-17 17:25:09-debug: run package(wave-editor) handler(enable) success!
2025-9-17 17:25:09-debug: run package(placeholder) handler(enable) start
2025-9-17 17:25:09-debug: run package(placeholder) handler(enable) success!
2025-9-17 17:25:14-debug: initialize scripting environment...
2025-9-17 17:25:14-debug: [[Executor]] prepare before lock
2025-9-17 17:25:14-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-17 17:25:14-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-17 17:25:14-debug: [[Executor]] prepare after unlock
2025-9-17 17:25:14-debug: Run asset db hook programming:afterPreStart success!
2025-9-17 17:25:14-debug: Start up the 'internal' database...
2025-9-17 17:25:14-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-17 17:25:15-debug: asset-db:worker-effect-data-processing (262ms)
2025-9-17 17:25:15-debug: asset-db-hook-programming-afterPreStart (5433ms)
2025-9-17 17:25:15-debug: asset-db-hook-engine-extends-afterPreStart (263ms)
2025-9-17 17:25:15-debug: Start up the 'assets' database...
2025-9-17 17:25:15-debug: asset-db:worker-startup-database[internal] (5706ms)
2025-9-17 17:25:15-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.47MB, end 195.92MB, increase: 20.44MB
2025-9-17 17:25:15-debug: lazy register asset handler directory
2025-9-17 17:25:15-debug: lazy register asset handler text
2025-9-17 17:25:15-debug: lazy register asset handler *
2025-9-17 17:25:15-debug: lazy register asset handler json
2025-9-17 17:25:15-debug: lazy register asset handler spine-data
2025-9-17 17:25:15-debug: lazy register asset handler dragonbones-atlas
2025-9-17 17:25:15-debug: lazy register asset handler javascript
2025-9-17 17:25:15-debug: lazy register asset handler dragonbones
2025-9-17 17:25:15-debug: lazy register asset handler terrain
2025-9-17 17:25:15-debug: lazy register asset handler typescript
2025-9-17 17:25:15-debug: lazy register asset handler sprite-frame
2025-9-17 17:25:15-debug: lazy register asset handler prefab
2025-9-17 17:25:15-debug: lazy register asset handler scene
2025-9-17 17:25:15-debug: lazy register asset handler tiled-map
2025-9-17 17:25:15-debug: lazy register asset handler sign-image
2025-9-17 17:25:15-debug: lazy register asset handler image
2025-9-17 17:25:15-debug: lazy register asset handler buffer
2025-9-17 17:25:15-debug: lazy register asset handler alpha-image
2025-9-17 17:25:15-debug: lazy register asset handler texture
2025-9-17 17:25:15-debug: lazy register asset handler texture-cube
2025-9-17 17:25:15-debug: lazy register asset handler erp-texture-cube
2025-9-17 17:25:15-debug: lazy register asset handler render-texture
2025-9-17 17:25:15-debug: lazy register asset handler texture-cube-face
2025-9-17 17:25:15-debug: lazy register asset handler gltf
2025-9-17 17:25:15-debug: lazy register asset handler rt-sprite-frame
2025-9-17 17:25:15-debug: lazy register asset handler gltf-mesh
2025-9-17 17:25:15-debug: lazy register asset handler gltf-material
2025-9-17 17:25:15-debug: lazy register asset handler gltf-animation
2025-9-17 17:25:15-debug: lazy register asset handler gltf-embeded-image
2025-9-17 17:25:15-debug: lazy register asset handler gltf-skeleton
2025-9-17 17:25:15-debug: lazy register asset handler fbx
2025-9-17 17:25:15-debug: lazy register asset handler physics-material
2025-9-17 17:25:15-debug: lazy register asset handler effect
2025-9-17 17:25:15-debug: lazy register asset handler material
2025-9-17 17:25:15-debug: lazy register asset handler audio-clip
2025-9-17 17:25:15-debug: lazy register asset handler gltf-scene
2025-9-17 17:25:15-debug: lazy register asset handler effect-header
2025-9-17 17:25:15-debug: lazy register asset handler animation-graph-variant
2025-9-17 17:25:15-debug: lazy register asset handler animation-graph
2025-9-17 17:25:15-debug: lazy register asset handler bitmap-font
2025-9-17 17:25:15-debug: lazy register asset handler animation-mask
2025-9-17 17:25:15-debug: lazy register asset handler particle
2025-9-17 17:25:15-debug: lazy register asset handler animation-clip
2025-9-17 17:25:15-debug: lazy register asset handler auto-atlas
2025-9-17 17:25:15-debug: lazy register asset handler label-atlas
2025-9-17 17:25:15-debug: lazy register asset handler render-pipeline
2025-9-17 17:25:15-debug: lazy register asset handler sprite-atlas
2025-9-17 17:25:15-debug: lazy register asset handler render-flow
2025-9-17 17:25:15-debug: lazy register asset handler ttf-font
2025-9-17 17:25:15-debug: lazy register asset handler render-stage
2025-9-17 17:25:15-debug: lazy register asset handler instantiation-mesh
2025-9-17 17:25:15-debug: lazy register asset handler instantiation-material
2025-9-17 17:25:15-debug: lazy register asset handler instantiation-animation
2025-9-17 17:25:15-debug: lazy register asset handler instantiation-skeleton
2025-9-17 17:25:15-debug: lazy register asset handler video-clip
2025-9-17 17:25:15-debug: asset-db:worker-startup-database[assets] (5677ms)
2025-9-17 17:25:15-debug: asset-db:start-database (5796ms)
2025-9-17 17:25:15-debug: asset-db:ready (9381ms)
2025-9-17 17:25:15-debug: fix the bug of updateDefaultUserData
2025-9-17 17:25:15-debug: init worker message success
2025-9-17 17:25:15-debug: programming:execute-script (-7ms)
2025-9-17 17:25:15-debug: [Build Memory track]: builder:worker-init start:194.31MB, end 207.06MB, increase: 12.75MB
2025-9-17 17:25:15-debug: builder:worker-init (322ms)
2025-9-17 17:30:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:30:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:30:32-debug: refresh db internal success
2025-9-17 17:30:32-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:30:32-debug: refresh db assets success
2025-9-17 17:30:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:30:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:30:32-debug: asset-db:refresh-all-database (116ms)
2025-9-17 17:30:32-debug: asset-db:worker-effect-data-processing (-3ms)
2025-9-17 17:30:32-debug: asset-db-hook-engine-extends-afterRefresh (-2ms)
2025-9-17 17:45:39-debug: refresh db internal success
2025-9-17 17:45:39-debug: refresh db assets success
2025-9-17 17:45:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:45:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:45:39-debug: asset-db:refresh-all-database (141ms)
2025-9-17 17:45:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:45:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:46:21-debug: refresh db internal success
2025-9-17 17:46:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:46:21-debug: refresh db assets success
2025-9-17 17:46:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:46:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:46:21-debug: asset-db:refresh-all-database (144ms)
2025-9-17 17:46:21-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 17:46:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:46:22-debug: Query all assets info in project
2025-9-17 17:46:22-debug: Skip compress image, progress: 0%
2025-9-17 17:46:22-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:46:22-debug: Init all bundles start..., progress: 0%
2025-9-17 17:46:22-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:22-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:46:22-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:46:22-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:46:22-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:46:22-debug:   Number of all scripts: 235
2025-9-17 17:46:22-debug:   Number of all scenes: 9
2025-9-17 17:46:22-debug:   Number of other assets: 1804
2025-9-17 17:46:22-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:46:22-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-17 17:46:22-debug: [Build Memory track]: 查询 Asset Bundle start:213.40MB, end 215.59MB, increase: 2.18MB
2025-9-17 17:46:22-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-9-17 17:46:22-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:46:22-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:22-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-17 17:46:22-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-17 17:46:22-debug: [Build Memory track]: 查询 Asset Bundle start:215.62MB, end 215.89MB, increase: 271.00KB
2025-9-17 17:46:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:46:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:46:22-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:46:22-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:46:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.92MB, end 215.96MB, increase: 41.74KB
2025-9-17 17:46:22-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:46:22-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:46:22-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:46:22-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.00MB, end 216.03MB, increase: 31.80KB
2025-9-17 17:46:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:46:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-17 17:46:22-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-17 17:46:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.08MB, end 213.48MB, increase: -2657.33KB
2025-9-17 17:46:23-debug: Query all assets info in project
2025-9-17 17:46:23-debug: Query all assets info in project
2025-9-17 17:46:23-debug: Query all assets info in project
2025-9-17 17:46:23-debug: Query all assets info in project
2025-9-17 17:46:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:46:23-debug: Skip compress image, progress: 0%
2025-9-17 17:46:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:46:23-debug: Skip compress image, progress: 0%
2025-9-17 17:46:23-debug: Skip compress image, progress: 0%
2025-9-17 17:46:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:46:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:46:23-debug: Skip compress image, progress: 0%
2025-9-17 17:46:23-debug: Init all bundles start..., progress: 0%
2025-9-17 17:46:23-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:46:23-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:46:23-debug: Init all bundles start..., progress: 0%
2025-9-17 17:46:23-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:46:23-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:46:23-debug: Init all bundles start..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:46:23-debug: Init all bundles start..., progress: 0%
2025-9-17 17:46:23-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:46:23-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:46:23-debug:   Number of all scripts: 235
2025-9-17 17:46:23-debug:   Number of all scenes: 9
2025-9-17 17:46:23-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:46:23-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:46:23-debug:   Number of other assets: 1804
2025-9-17 17:46:23-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:46:23-debug:   Number of all scenes: 9
2025-9-17 17:46:23-debug:   Number of all scripts: 235
2025-9-17 17:46:23-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-9-17 17:46:23-debug:   Number of other assets: 1804
2025-9-17 17:46:23-debug: [Build Memory track]: 查询 Asset Bundle start:214.19MB, end 217.42MB, increase: 3.24MB
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:46:23-debug: [Build Memory track]: 查询 Asset Bundle start:217.46MB, end 217.50MB, increase: 36.67KB
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:46:23-debug: [Build Memory track]: 查询 Asset Bundle start:217.53MB, end 218.03MB, increase: 511.66KB
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-17 17:46:23-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.15MB, end 218.18MB, increase: 32.40KB
2025-9-17 17:46:23-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:46:23-debug:   Number of all scenes: 9
2025-9-17 17:46:23-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:46:23-debug:   Number of all scripts: 235
2025-9-17 17:46:23-debug:   Number of other assets: 1804
2025-9-17 17:46:23-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:46:23-debug:   Number of all scenes: 9
2025-9-17 17:46:23-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:46:23-debug:   Number of all scripts: 235
2025-9-17 17:46:23-debug:   Number of other assets: 1804
2025-9-17 17:46:23-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:46:23-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ---- (51ms)
2025-9-17 17:46:23-log: run build task 填充脚本数据到 settings.json success in 51 ms√, progress: 13%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.18MB, end 206.21MB, increase: 33.63KB
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:46:23-debug: [Build Memory track]: 查询 Asset Bundle start:206.28MB, end 206.30MB, increase: 14.70KB
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ---- (7ms)
2025-9-17 17:46:23-log: run build task 填充脚本数据到 settings.json success in 7 ms√, progress: 13%
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-9-17 17:46:23-debug: [Build Memory track]: 查询 Asset Bundle start:206.33MB, end 206.89MB, increase: 568.92KB
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-17 17:46:23-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.97MB, end 207.00MB, increase: 38.41KB
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-17 17:46:23-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:46:23-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:46:23-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:46:23-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.09MB, end 207.59MB, increase: 509.96KB
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:46:23-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-9-17 17:46:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:46:23-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:46:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-17 17:46:23-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.68MB, end 207.78MB, increase: 99.73KB
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:46:23-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:46:26-debug: refresh db internal success
2025-9-17 17:46:27-debug: refresh db assets success
2025-9-17 17:46:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:46:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:46:27-debug: asset-db:refresh-all-database (139ms)
2025-9-17 17:46:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:46:27-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:48:08-debug: refresh db internal success
2025-9-17 17:48:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:48:08-debug: refresh db assets success
2025-9-17 17:48:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:48:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:48:08-debug: asset-db:refresh-all-database (141ms)
2025-9-17 17:48:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:48:13-debug: Query all assets info in project
2025-9-17 17:48:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:48:13-debug: Skip compress image, progress: 0%
2025-9-17 17:48:13-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:48:13-debug: Init all bundles start..., progress: 0%
2025-9-17 17:48:13-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:48:13-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:48:13-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:48:13-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:48:13-debug:   Number of all scripts: 235
2025-9-17 17:48:13-debug:   Number of other assets: 1804
2025-9-17 17:48:13-debug:   Number of all scenes: 9
2025-9-17 17:48:13-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:48:13-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-17 17:48:13-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-9-17 17:48:13-debug: [Build Memory track]: 查询 Asset Bundle start:210.47MB, end 212.07MB, increase: 1.60MB
2025-9-17 17:48:13-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:48:13-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:48:13-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-17 17:48:13-debug: [Build Memory track]: 查询 Asset Bundle start:212.11MB, end 212.37MB, increase: 272.12KB
2025-9-17 17:48:13-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-17 17:48:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:48:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:48:13-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-17 17:48:13-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:48:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.41MB, end 212.43MB, increase: 19.77KB
2025-9-17 17:48:13-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:48:13-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-17 17:48:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:48:13-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.47MB, end 212.49MB, increase: 19.23KB
2025-9-17 17:48:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:48:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:48:13-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-17 17:48:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.52MB, end 211.33MB, increase: -1221.02KB
2025-9-17 17:48:15-debug: refresh db internal success
2025-9-17 17:48:15-debug: refresh db assets success
2025-9-17 17:48:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:48:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:48:15-debug: asset-db:refresh-all-database (105ms)
2025-9-17 17:48:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:48:25-debug: refresh db internal success
2025-9-17 17:48:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:48:25-debug: refresh db assets success
2025-9-17 17:48:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:48:25-debug: asset-db:refresh-all-database (105ms)
2025-9-17 18:01:47-debug: refresh db internal success
2025-9-17 18:01:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:01:47-debug: refresh db assets success
2025-9-17 18:01:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:01:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:01:47-debug: asset-db:refresh-all-database (139ms)
2025-9-17 18:01:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 18:01:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 18:03:08-debug: refresh db internal success
2025-9-17 18:03:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:03:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:03:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:03:08-debug: refresh db assets success
2025-9-17 18:03:08-debug: asset-db:refresh-all-database (104ms)
2025-9-17 18:03:10-debug: Query all assets info in project
2025-9-17 18:03:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 18:03:10-debug: Skip compress image, progress: 0%
2025-9-17 18:03:10-debug: Init all bundles start..., progress: 0%
2025-9-17 18:03:10-debug: Num of bundles: 16..., progress: 0%
2025-9-17 18:03:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 18:03:10-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 18:03:10-debug: Init bundle root assets start..., progress: 0%
2025-9-17 18:03:10-debug:   Number of all scenes: 9
2025-9-17 18:03:10-debug:   Number of other assets: 1804
2025-9-17 18:03:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 18:03:10-debug:   Number of all scripts: 235
2025-9-17 18:03:10-debug: Init bundle root assets success..., progress: 0%
2025-9-17 18:03:10-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-17 18:03:10-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-9-17 18:03:10-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 18:03:10-debug: [Build Memory track]: 查询 Asset Bundle start:219.78MB, end 221.21MB, increase: 1.43MB
2025-9-17 18:03:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 18:03:10-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-17 18:03:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 18:03:10-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-17 18:03:10-debug: [Build Memory track]: 查询 Asset Bundle start:221.24MB, end 221.54MB, increase: 306.40KB
2025-9-17 18:03:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 18:03:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-17 18:03:10-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-17 18:03:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 18:03:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.57MB, end 221.60MB, increase: 26.74KB
2025-9-17 18:03:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 18:03:10-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-17 18:03:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 18:03:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.62MB, end 221.64MB, increase: 16.72KB
2025-9-17 18:03:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 18:03:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 18:03:10-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-17 18:03:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.67MB, end 221.95MB, increase: 291.54KB
2025-9-17 18:03:27-debug: refresh db internal success
2025-9-17 18:03:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:03:27-debug: refresh db assets success
2025-9-17 18:03:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:03:27-debug: asset-db:refresh-all-database (136ms)
2025-9-17 18:03:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 18:06:15-debug: refresh db internal success
2025-9-17 18:06:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:06:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:06:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:06:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:06:16-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 18:06:16-debug: refresh db assets success
2025-9-17 18:06:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:06:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:06:16-debug: asset-db:refresh-all-database (136ms)
2025-9-17 18:06:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 18:11:47-debug: refresh db internal success
2025-9-17 18:11:47-debug: refresh db assets success
2025-9-17 18:11:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:11:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:11:47-debug: asset-db:refresh-all-database (144ms)
2025-9-17 18:11:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 18:11:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 18:11:52-debug: refresh db internal success
2025-9-17 18:11:52-debug: refresh db assets success
2025-9-17 18:11:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 18:11:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 18:11:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 18:11:52-debug: asset-db:refresh-all-database (134ms)
2025-9-17 18:11:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 09:09:59-debug: refresh db internal success
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresenemy.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestask.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresskill.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss\BossPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:09:59-debug: refresh db assets success
2025-9-18 09:09:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 09:09:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 09:09:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-18 09:09:59-debug: asset-db:refresh-all-database (247ms)
2025-9-18 09:09:59-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-18 09:49:57-debug: refresh db internal success
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\randTerrain
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\data\MainPlaneFightData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randomTer_2.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randomTer_1.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randomTer_3.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randtest1.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\GameReviveUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\game\GameReviveUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Node_Element_ToLand2.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Node_Element_ToWater.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_03.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_04.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_05.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_06.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_07.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_08.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_09.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_10.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_11.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_12.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_13.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Island_14.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_StartCloud02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_StartCloud01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandomElement_Island_01 x2.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandomElement_Island_02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandomElement_StartCloud.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToLand2.png
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToWater.png
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\RandomElement01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island2.png
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToWater.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToLand2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island6.png
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToWater.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Node_Element_ToLand2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island6.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Element_Island6.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\game
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000001.json
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane\StateMachine.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\EmittierTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameRuleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\RandomElement02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Mount8.png
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cReImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Mount8.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: %cReImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Mount8.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-18 09:49:57-debug: refresh db assets success
2025-9-18 09:49:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 09:49:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 09:49:57-debug: asset-db:refresh-all-database (359ms)
2025-9-18 09:49:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 10:24:28-debug: refresh db internal success
2025-9-18 10:24:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:24:28-debug: refresh db assets success
2025-9-18 10:24:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:24:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:24:28-debug: asset-db:refresh-all-database (176ms)
2025-9-18 10:24:28-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-18 10:24:32-debug: refresh db internal success
2025-9-18 10:24:32-debug: refresh db assets success
2025-9-18 10:24:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:24:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:24:32-debug: asset-db:refresh-all-database (135ms)
2025-9-18 10:26:22-debug: refresh db internal success
2025-9-18 10:26:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:26:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:26:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:26:22-debug: refresh db assets success
2025-9-18 10:26:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:26:22-debug: asset-db:refresh-all-database (145ms)
2025-9-18 10:26:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-18 10:26:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 10:53:43-debug: refresh db internal success
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\IEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 10:53:43-debug: refresh db assets success
2025-9-18 10:53:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:53:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:53:43-debug: asset-db:refresh-all-database (196ms)
2025-9-18 10:53:47-debug: refresh db internal success
2025-9-18 10:53:47-debug: refresh db assets success
2025-9-18 10:53:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:53:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:53:47-debug: asset-db:refresh-all-database (107ms)
2025-9-18 10:53:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 10:54:33-debug: refresh db internal success
2025-9-18 10:54:33-debug: refresh db assets success
2025-9-18 10:54:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 10:54:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 10:54:33-debug: asset-db:refresh-all-database (131ms)
2025-9-18 11:17:02-debug: refresh db internal success
2025-9-18 11:17:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:17:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:17:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:17:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:17:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:17:02-debug: refresh db assets success
2025-9-18 11:17:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:17:02-debug: asset-db:refresh-all-database (123ms)
2025-9-18 11:17:47-debug: refresh db internal success
2025-9-18 11:17:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:17:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:17:47-debug: refresh db assets success
2025-9-18 11:17:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:17:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-18 11:17:47-debug: asset-db:refresh-all-database (109ms)
2025-9-18 11:17:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 11:18:09-debug: refresh db internal success
2025-9-18 11:18:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:18:09-debug: refresh db assets success
2025-9-18 11:18:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:18:09-debug: asset-db:refresh-all-database (103ms)
2025-9-18 11:52:24-debug: refresh db internal success
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:52:25-debug: refresh db assets success
2025-9-18 11:52:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:52:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:52:25-debug: asset-db:refresh-all-database (175ms)
2025-9-18 11:52:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-18 11:52:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 11:53:07-debug: refresh db internal success
2025-9-18 11:53:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 11:53:07-debug: refresh db assets success
2025-9-18 11:53:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:53:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:53:07-debug: asset-db:refresh-all-database (131ms)
2025-9-18 11:53:25-debug: refresh db internal success
2025-9-18 11:53:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:53:26-debug: refresh db assets success
2025-9-18 11:53:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:53:26-debug: asset-db:refresh-all-database (101ms)
2025-9-18 11:53:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 11:53:34-debug: refresh db internal success
2025-9-18 11:53:34-debug: refresh db assets success
2025-9-18 11:53:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:53:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:53:34-debug: asset-db:refresh-all-database (111ms)
2025-9-18 11:53:34-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-18 11:53:34-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-18 11:56:09-debug: refresh db internal success
2025-9-18 11:56:09-debug: refresh db assets success
2025-9-18 11:56:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 11:56:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 11:56:09-debug: asset-db:refresh-all-database (109ms)
2025-9-18 11:56:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-18 11:56:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-18 12:13:58-debug: %cDestroy%c: E:\M2Game\Client\assets\editor\gizmos\WaveGizmo.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-18 12:13:58-debug: refresh db internal success
2025-9-18 12:13:59-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos
background: #aaff85; color: #000;
color: #000;
2025-9-18 12:13:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 12:13:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 12:13:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 12:13:59-debug: refresh db assets success
2025-9-18 12:13:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 12:13:59-debug: asset-db:refresh-all-database (210ms)
2025-9-18 12:13:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-18 14:30:02-debug: refresh db internal success
2025-9-18 14:30:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\EventGroupCom.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:30:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:30:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:30:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:30:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\WaveEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:30:03-debug: refresh db assets success
2025-9-18 14:30:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 14:30:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 14:30:03-debug: asset-db:refresh-all-database (319ms)
2025-9-18 14:30:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-18 14:41:19-debug: refresh db internal success
2025-9-18 14:41:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-9-18 14:41:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-18 14:41:19-debug: refresh db assets success
2025-9-18 14:41:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-18 14:41:19-debug: asset-db:refresh-all-database (128ms)
2025-9-18 14:41:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-18 14:41:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
