import { _decorator, Label, Vec3 } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass('PopupUI')
export class PopupUI extends BaseUI {
    @property(Label)
    info: Label | null = null;

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;

    @property(ButtonPlus)
    btnCancel: ButtonPlus | null = null;

    onConfirm: Function | undefined = undefined;
    onCancel: Function | undefined = undefined;

    public static getUrl(): string { return "prefab/ui/PopupUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: true }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onConfirmClick, this);
        this.btnCancel!.addClick(this.onCancelClick, this);
    }
    async onConfirmClick() {
        UIMgr.closeUI(PopupUI);
        if (this.onConfirm) {
            this.onConfirm();
        }
    }
    async onCancelClick() {
        UIMgr.closeUI(PopupUI);
        if (this.onCancel) {
            this.onCancel();
        }
    }
    async onShow(content: string,
        onConfirm: Function,
        onCancel: Function): Promise<void> {
        this.info!.string = content;
        this.onConfirm = onConfirm;
        this.onCancel = onCancel;
        if (this.onCancel) {

        } else {
            this.btnCancel!.node.active = false;
            this.btnOK!.node.position = new Vec3(0, this.btnOK!.node.position.y, 0);
        }
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

}
