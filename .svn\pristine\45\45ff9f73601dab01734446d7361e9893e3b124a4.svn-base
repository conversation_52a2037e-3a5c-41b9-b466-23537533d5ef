import { EventActionBase } from "db://assets/bundles/common/script/game/bullet/actions/IEventAction";
import { IEventGroupContext, Comparer } from "db://assets/bundles/common/script/game/bullet/EventGroup";
import { WaveEventGroupContext } from "./WaveEventGroup";

export class WaveActionBase extends EventActionBase {
    onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this.resetStartValue(context as WaveEventGroupContext);
        this.resetTargetValue(context as WaveEventGroupContext);
    }

    onExecute(context: IEventGroupContext, dt: number): void {
        this._elapsedTime += dt;
        if (this._elapsedTime < this._delay) {
            return;
        }

        if (this._elapsedTime >= this._duration) {
            this.onExecuteInternal(context as WaveEventGroupContext, this._targetValue);
            this._isCompleted = true;
        }
        else if (this.canLerp()) {
            this.onExecuteInternal(context as WaveEventGroupContext, this.lerpValue(this._startValue, this._targetValue));
        }
    }

    // overload EventActionBase methods
    protected resetStartValue(context: WaveEventGroupContext): void {
    }

    protected resetTargetValue(context: WaveEventGroupContext): void {
    }

    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {
    }
}

