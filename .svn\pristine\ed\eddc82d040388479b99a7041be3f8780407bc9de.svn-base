import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { IData } from "db://assets/bundles/common/script/data/DataManager";
import { logError } from "db://assets/scripts/utils/Logger";
import { ResTaskClass } from "../../autogen/luban/schema";

export class Task implements IData {
    // 任务集合
    taskMap: Map<ResTaskClass, csproto.cs.ICSTaskInfo[]> = new Map();

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this)
    }

    // 任务奖励
    private onGetTaskRewardMsg(msg: csproto.cs.IS2CMsg) {
        // const taskId = msg.body?.task_get_reward?.reward_list
        // if (taskId) {
        //     const taskCfg = MyApp.lubanTables.TbResTask.get();
        //     if (taskCfg) {
        //         this.taskMap.set(taskCfg.taskClass, t);
        //     }
        // }
    }

    // 全任务信息
    private onGetTaskInfoMsg(msg: csproto.cs.IS2CMsg) {
        const taskList = msg.body?.task_get_info?.task_list || [];
        taskList.forEach(t => {
            const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!)
            if (!taskCfg) {
                logError("Task", `task id ${t.task_id} not found`)
                return
            }
            let taskList = this.taskMap.get(taskCfg.taskClass) || [];
            taskList.push(t);
            this.taskMap.set(taskCfg.taskClass, taskList);
        })
    }

    getTaskListByClass(taskClass: ResTaskClass): csproto.cs.ICSTaskInfo[] {
        return this.taskMap.get(taskClass) || [];
    }
    getTaskByTaskId(taskId: number, taskClass?: ResTaskClass): csproto.cs.ICSTaskInfo | undefined {
        if (taskClass) {
            const taskList = this.taskMap.get(taskClass) || [];
            return taskList.find(t => t.task_id === taskId);
        }
        for (const taskList of this.taskMap.values()) {
            const task = taskList.find(t => t.task_id === taskId);
            if (task) {
                return task;
            }
        }
        return undefined;
    }

    update(): void {
    }
}
