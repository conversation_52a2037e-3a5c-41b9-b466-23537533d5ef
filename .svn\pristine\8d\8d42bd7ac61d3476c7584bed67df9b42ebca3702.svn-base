
import { Prefab, instantiate } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import GameResourceList from "../const/GameResourceList";
import { MainPlaneData } from "../data/MainPlaneFightData";
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";
import { MainPlane } from "../ui/plane/mainPlane/MainPlane";



export class MainPlaneManager extends SingletonBase<MainPlaneManager> {

    _planeData: PlaneData | null = null;//飞机数据
    mainPlane: MainPlane | null = null;//飞机战斗UI

    planeFightData: MainPlaneData = new MainPlaneData();//飞机战斗数据
    hurtTotal: number = 0;


    set moveAble(value: boolean) {
        if (this.mainPlane) {
            this.mainPlane.setMoveAble(value);
        }
    }

    setPlaneData(planeData: PlaneData) {
        this._planeData = planeData;
    }

    async preload() {
        GameIns.battleManager.addLoadCount(1);
        await this.createMainPlane();
        GameIns.battleManager.checkLoadFinish();
        this.reset();
    }

    /**
     * 创建主飞机
     * @param isTrans 是否为特殊状态
     * @returns 主飞机对象
     */
    async createMainPlane(): Promise<MainPlane | null> {
        if (this.mainPlane) {
            return this.mainPlane;
        }

        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);
        let planeNode = instantiate(prefab);
        this.mainPlane = planeNode.getComponent(MainPlane)
        BattleLayer.me?.addMainPlane();
        this.mainPlane?.initPlane(this._planeData!);
        return this.mainPlane;
    }

    mainReset() {
        if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
        }
    }

    reset() {
        this.planeFightData.die = false;

        if (this.mainPlane) {
            this.mainPlane.battleQuit();
        }

        this.refreshPlaneData();
    }

    refreshPlaneData(): void {
        this.planeFightData.maxhp = this._planeData!.getMaxHP()
        this.planeFightData.hp = this.planeFightData.maxhp
    }

    idToType(id: number): number {
        return Math.floor(id / 100);
    }
}
