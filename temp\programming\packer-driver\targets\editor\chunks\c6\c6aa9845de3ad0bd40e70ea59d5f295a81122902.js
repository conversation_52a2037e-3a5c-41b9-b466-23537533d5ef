System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventConditionBase, _crd;

  function _reportPossibleCrUseOfIEventConditionData(extras) {
    _reporterNs.report("IEventConditionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "../EventGroup", _context.meta, extras);
  }

  _export("EventConditionBase", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b4989OWpMhNsKm4kTnR6YHD", "IEventCondition", undefined); // Base interfaces


      _export("EventConditionBase", EventConditionBase = class EventConditionBase {
        constructor(data) {
          this.data = void 0;
          this._targetValue = 0;
          this.data = data;
        }

        onLoad(context) {
          this._targetValue = this.data.targetValue.eval();
        }

        evaluate(context) {
          // Default implementation (can be overridden)
          return true;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c6aa9845de3ad0bd40e70ea59d5f295a81122902.js.map