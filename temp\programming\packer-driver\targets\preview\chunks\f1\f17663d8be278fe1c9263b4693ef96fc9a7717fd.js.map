{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts"], "names": ["_decorator", "Vec2", "Enum", "CCInteger", "ExpressionValue", "eCompareOp", "eConditionOp", "eTargetValueType", "eWrapMode", "eEasing", "ccclass", "property", "eSpawnOrder", "eWaveAngleType", "eWaveCompletion", "eWaveConditionType", "eWaveActionType", "eWaveConditionTypeCn", "eWaveActionTypeCn", "WaveConditionData", "type", "displayName", "visible", "typeCn", "value", "targetValueStr", "targetValue", "raw", "And", "Player_Level", "Equal", "WaveActionData", "editor<PERSON><PERSON><PERSON>", "tooltip", "delayStr", "delay", "durationStr", "duration", "transitionDurationStr", "transitionDuration", "Spawn_Interval", "Absolute", "Once", "Linear", "WaveEventGroupData", "SpawnGroup", "serializable", "WaveData", "_spawnPos", "waveCompletionParamStr", "waveCompletionParam", "spawnIntervalStr", "spawnInterval", "spawnPosXStr", "spawnPosX", "spawnPosYStr", "spawnPosY", "spawnPos", "set", "eval", "spawnAngleStr", "spawnAngle", "Random", "SpawnCount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,e,iBAAAA,e;;AACqBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,Y,iBAAAA,Y;AAAgCC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,S,iBAAAA,S;;AACnFC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;6BAElBY,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;gCAKAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;iCACYC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;;;oCAIAC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;iCAMAC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;;;sCAcAC,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;mCAMAC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;cAaZ;;;mCAEaC,iB,WADZT,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI;AAAA;AAAA,yCAAZ;AAA4BmB,QAAAA,WAAW,EAAE;AAAzC,OAAD,C,UAGRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERX,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI,CAACe,oBAAD,CAAZ;AAAoCI,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,UAIRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI;AAAA;AAAA,qCAAZ;AAA0BmB,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,UAIRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BAjBb,MACaF,iBADb,CAC8D;AAAA;AAAA;;AAAA;;AAAA;;AAa1D;AAb0D;AAAA;;AAOzC,YAANI,MAAM,GAAyB;AAAE,iBAAO,KAAKH,IAAZ;AAAuD;;AAClF,YAANG,MAAM,CAACC,KAAD,EAA8B;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAsD;;AAS9E,YAAdC,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACD,KAAD,EAAgB;AAAE,eAAKE,WAAL,CAAiBC,GAAjB,GAAuBH,KAAvB;AAA+B;;AAlBhB,O;;;;;iBAEhC;AAAA;AAAA,4CAAaI,G;;;;;;;iBAGLb,kBAAkB,CAACc,Y;;;;;;;iBAMtB;AAAA;AAAA,wCAAWC,K;;;;;;;iBAIH;AAAA;AAAA,kDAAoB,GAApB,C;;;;gCAO9BC,c,YADZrB,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAAC;AAAEU,QAAAA,WAAW,EAAE,eAAf;AAAgCW,QAAAA,UAAU,EAAE;AAA5C,OAAD,C,WAGRrB,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI,CAACgB,iBAAD,CAAZ;AAAiCG,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,WAIRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAAEU,QAAAA,WAAW,EAAE,MAAf;AAAuBY,QAAAA,OAAO,EAAE;AAAhC,OAAD,C,WAKRtB,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAAEU,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI;AAAA;AAAA,iDAAZ;AAAgCmB,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAGRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAAEU,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI;AAAA;AAAA,mCAAZ;AAAyBmB,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,WAGRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAElB,IAAI;AAAA;AAAA,+BAAZ;AAAuBmB,QAAAA,WAAW,EAAE;AAApC,OAAD,C,6BA1Cb,MACaU,cADb,CACwD;AAAA;AAAA;;AAAA;;AAAA;;AAgBpD;AAhBoD;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAOnC,YAANR,MAAM,GAAsB;AAAE,iBAAO,KAAKH,IAAZ;AAAoD;;AAC5E,YAANG,MAAM,CAACC,KAAD,EAA2B;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAAmD;;AAK9E,YAARU,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWX,KAAlB;AAA0B;;AACvC,YAARU,QAAQ,CAACV,KAAD,EAAgB;AAAE,eAAKW,KAAL,CAAWX,KAAX,GAAmBA,KAAnB;AAA2B;;AAM1C,YAAXY,WAAW,GAAW;AAAE,iBAAO,KAAKC,QAAL,CAAcV,GAArB;AAA2B;;AACxC,YAAXS,WAAW,CAACZ,KAAD,EAAgB;AAAE,eAAKa,QAAL,CAAcV,GAAd,GAAoBH,KAApB;AAA4B;;AAK3C,YAAdC,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACD,KAAD,EAAgB;AAAE,eAAKE,WAAL,CAAiBC,GAAjB,GAAuBH,KAAvB;AAA+B;;AAQ1C,YAArBc,qBAAqB,GAAW;AAAE,iBAAO,KAAKC,kBAAL,CAAwBf,KAA/B;AAAuC;;AACpD,YAArBc,qBAAqB,CAACd,KAAD,EAAgB;AAAE,eAAKe,kBAAL,CAAwBf,KAAxB,GAAgCA,KAAhC;AAAwC;;AApCtC,O;;;;;iBAE9B,E;;;;;;;iBAGSR,eAAe,CAACwB,c;;;;;;;iBAMd;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAOE;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMG;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMK;AAAA;AAAA,oDAAiBC,Q;;;;;;;iBAGd;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMxB;AAAA;AAAA,sCAAUC,I;;;;;;;iBAGP;AAAA;AAAA,kCAAQC,M;;;;oCAIxBC,kB,aADZlC,OAAO,CAAC,oBAAD,C,WAEHC,QAAQ,CAAC;AAAEU,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE,CAACD,iBAAD,CAAR;AAA6BE,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAGRV,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE,CAACW,cAAD,CAAR;AAA0BV,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,8BARb,MACauB,kBADb,CACgC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEN,E;;;;;;;iBAGmB,E;;;;;;;iBAGN,E;;;;4BAI1BC,U,aADZnC,OAAO,CAAC,YAAD,C,WAEHC,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEjB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEjB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC,KAAT;AAAgBwB,QAAAA,YAAY,EAAE;AAA9B,OAAD,C,gCARb,MACaD,UADb,CACwB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEF,C;;;;;;;iBAGD,E;;;;;;;iBAGI,C;;;AAGzB;AACA;AACA;AACA;;;0BAEaE,Q,aADZrC,OAAO,CAAC,UAAD,C,WAIHC,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAE,CAACyB,UAAD,CAAP;AAAqBxB,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAElB,IAAI,CAACU,WAAD,CAAX;AAA0BS,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,WAQRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAElB,IAAI,CAACY,eAAD,CAAX;AAA8BO,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,WAGRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRV,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAURV,QAAQ,CAAC;AAACW,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERX,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WA4BRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAE,CAACwB,kBAAD,CAAP;AAA6BvB,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,gCA7Eb,MACa0B,QADb,CACsB;AAAA;AAClB;AACA;AAFkB;;AAAA;;AASlB;AACA;AACA;AACA;AACA;AAbkB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAwCVC,SAxCU,GAwCQ,IAAI/C,IAAJ,EAxCR;;AAAA;;AAoDlB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AA1EkB;AAAA;;AAoBe,YAAtBgD,sBAAsB,GAAW;AAAE,iBAAO,KAAKC,mBAAL,CAAyBvB,GAAhC;AAAsC;;AACnD,YAAtBsB,sBAAsB,CAACzB,KAAD,EAAgB;AAAE,eAAK0B,mBAAL,CAAyBvB,GAAzB,GAA+BH,KAA/B;AAAuC;;AAK/D,YAAhB2B,gBAAgB,GAAW;AAAE,iBAAO,KAAKC,aAAL,CAAmBzB,GAA1B;AAAgC;;AAC7C,YAAhBwB,gBAAgB,CAAC3B,KAAD,EAAgB;AAAE,eAAK4B,aAAL,CAAmBzB,GAAnB,GAAyBH,KAAzB;AAAiC;;AAOvD,YAAZ6B,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe3B,GAAtB;AAA4B;;AACzC,YAAZ0B,YAAY,CAAC7B,KAAD,EAAgB;AAAE,eAAK8B,SAAL,CAAe3B,GAAf,GAAqBH,KAArB;AAA6B;;AAE/C,YAAZ+B,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe7B,GAAtB;AAA4B;;AACzC,YAAZ4B,YAAY,CAAC/B,KAAD,EAAgB;AAAE,eAAKgC,SAAL,CAAe7B,GAAf,GAAqBH,KAArB;AAA6B;;AAGnD,YAARiC,QAAQ,GAAS;AACxB,eAAKT,SAAL,CAAeU,GAAf,CAAmB,KAAKJ,SAAL,CAAeK,IAAf,EAAnB,EAA0C,KAAKH,SAAL,CAAeG,IAAf,EAA1C;;AACA,iBAAO,KAAKX,SAAZ;AACH;;AAKuB,YAAbY,aAAa,GAAW;AAAE,iBAAO,KAAKC,UAAL,CAAgBlC,GAAvB;AAA6B;;AAC1C,YAAbiC,aAAa,CAACpC,KAAD,EAAgB;AAAE,eAAKqC,UAAL,CAAgBlC,GAAhB,GAAsBH,KAAtB;AAA8B;;AAlDtD,O;;;;;iBAIgB,E;;;;;;;iBAGDZ,WAAW,CAACkD,M;;;;;;;iBAQJhD,eAAe,CAACiD,U;;;;;;;iBAGV;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMP;AAAA;AAAA,kDAAoB,MAApB,C;;;;;;;iBAMH;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAEA;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAeA;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBA8BS,E", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2, Prefab, Enum, CCInteger, CCFloat } from \"cc\";\r\nimport { ExpressionValue } from \"./bullet/ExpressionValue\";\r\nimport { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType, eWrapMode } from \"./bullet/EventGroupData\";\r\nimport { eEasing } from \"../bullet/Easing\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eSpawnOrder {\r\n    Sequential = 0,\r\n    Random = 1,\r\n}\r\n\r\nexport enum eWaveAngleType {\r\n    FacingMoveDir, FacingPlayer, Fixed, \r\n}\r\n\r\n// 波次完成条件\r\nexport enum eWaveCompletion {\r\n    Time, SpawnCount\r\n}\r\n\r\nexport enum eWaveConditionType {\r\n    Player_Level,       // 玩家等级\r\n\r\n    Spawn_Count,        // 当前波次生成数量\r\n}\r\n\r\nexport enum eWaveActionType {\r\n    Spawn_Interval,      // 出生间隔\r\n    Spawn_Angle,         // 出生角度\r\n\r\n    // Plane_Speed,        // 飞机速度\r\n    // Plane_Angle,        // 飞机角度\r\n    // Plane_Accelerate,   // 飞机加速度\r\n    // Plane_AccelerateAngle,  // 飞机加速度角度\r\n    // Plane_OrientationType,  // 飞机朝向类型\r\n    // Plane_TurnSpeed,        // 飞机转向速度\r\n    // Plane_TiltingDistance,  // 飞机振荡距离\r\n    // Plane_TiltingSpeed,     // 飞机振荡速度\r\n}\r\n\r\nexport enum eWaveConditionTypeCn {\r\n    玩家等级 = eWaveConditionType.Player_Level,\r\n    当前波次生成数量 = eWaveConditionType.Spawn_Count,\r\n\r\n}\r\n\r\nexport enum eWaveActionTypeCn {\r\n    出生间隔 = eWaveActionType.Spawn_Interval,\r\n    出生角度 = eWaveActionType.Spawn_Angle,\r\n    // 飞机速度 = eWaveActionType.Plane_Speed,\r\n    // 飞机角度 = eWaveActionType.Plane_Angle,\r\n    // 飞机加速度 = eWaveActionType.Plane_Accelerate,\r\n    // 飞机加速度角度 = eWaveActionType.Plane_AccelerateAngle,\r\n    // 飞机朝向类型 = eWaveActionType.Plane_OrientationType,\r\n    // 飞机转向速度 = eWaveActionType.Plane_TurnSpeed,\r\n    // 飞机振荡距离 = eWaveActionType.Plane_TiltingDistance,\r\n    // 飞机振荡速度 = eWaveActionType.Plane_TiltingSpeed,\r\n}\r\n\r\n// 和发射器的事件组类似\r\n@ccclass(\"WaveConditionData\")\r\nexport class WaveConditionData implements IEventConditionData {\r\n    @property({ type: Enum(eConditionOp), displayName: '条件关系' })\r\n    public op: eConditionOp = eConditionOp.And;\r\n\r\n    @property({visible:false})\r\n    public type: eWaveConditionType = eWaveConditionType.Player_Level;\r\n    @property({ type: Enum(eWaveConditionTypeCn), displayName: '条件类型' })\r\n    public get typeCn(): eWaveConditionTypeCn { return this.type  as unknown as eWaveConditionTypeCn; }\r\n    public set typeCn(value: eWaveConditionTypeCn) { this.type = value  as unknown as eWaveConditionType; }\r\n\r\n    @property({ type: Enum(eCompareOp), displayName: '比较方式' })\r\n    public compareOp: eCompareOp = eCompareOp.Equal;\r\n    \r\n    // 条件值: 例如持续时间、距离\r\n    @property({visible:false})\r\n    public targetValue : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n}\r\n\r\n@ccclass(\"WaveActionData\")\r\nexport class WaveActionData implements IEventActionData {\r\n    @property({ displayName: '行为名称(编辑器下调试用)', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({visible:false})\r\n    public type: eWaveActionType = eWaveActionType.Spawn_Interval;\r\n    @property({ type: Enum(eWaveActionTypeCn), displayName: '行为类型' })\r\n    public get typeCn(): eWaveActionTypeCn { return this.type  as unknown as eWaveActionTypeCn; }\r\n    public set typeCn(value: eWaveActionTypeCn) { this.type = value  as unknown as eWaveActionType; }\r\n    \r\n    @property({visible:false})\r\n    public delay : ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '延迟时间', tooltip: '事件触发后，延迟多少毫秒开始执行该行为' })\r\n    public get delayStr(): number { return this.delay.value; }\r\n    public set delayStr(value: number) { this.delay.value = value; }\r\n\r\n    // 持续时间: 0表示立即执行\r\n    @property({visible:false})\r\n    public duration: ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '持续时间' })\r\n    public get durationStr(): string { return this.duration.raw; }\r\n    public set durationStr(value: string) { this.duration.raw = value; }\r\n    \r\n    @property({visible:false})\r\n    public targetValue: ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n    \r\n    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })\r\n    public targetValueType: eTargetValueType = eTargetValueType.Absolute;\r\n    \r\n    @property({visible:false})\r\n    public transitionDuration : ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '变换到目标值所需时间' })\r\n    public get transitionDurationStr(): number { return this.transitionDuration.value; }\r\n    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }\r\n    \r\n    @property({ type: Enum(eWrapMode), displayName: '循环模式' })\r\n    wrapMode: eWrapMode = eWrapMode.Once;\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    public easing: eEasing = eEasing.Linear;\r\n}\r\n\r\n@ccclass(\"WaveEventGroupData\")\r\nexport class WaveEventGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: [WaveConditionData], displayName: '条件列表' })\r\n    public conditions: WaveConditionData[] = [];\r\n\r\n    @property({ type: [WaveActionData], displayName: '行为列表' })\r\n    public actions: WaveActionData[] = [];\r\n}\r\n\r\n@ccclass(\"SpawnGroup\")\r\nexport class SpawnGroup {\r\n    @property({type: CCInteger, displayName: \"飞机ID\"})\r\n    planeID: number = 0;\r\n\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    weight: number = 50;\r\n\r\n    @property({visible:false, serializable: false})\r\n    selfWeight: number = 0;\r\n}\r\n\r\n/**\r\n * 波次数据：未来代替现有的EnemyWave\r\n * 所有时间相关的，单位都是毫秒(ms)\r\n */\r\n@ccclass(\"WaveData\")\r\nexport class WaveData {\r\n    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发\r\n    // 因此这里不再配置触发条件\r\n    @property({type: [SpawnGroup], displayName: \"出生组\"})\r\n    public spawnGroup: SpawnGroup[] = [];\r\n\r\n    @property({type: Enum(eSpawnOrder), displayName: \"出生顺序\"})\r\n    public spawnOrder: eSpawnOrder = eSpawnOrder.Random;\r\n\r\n    // @property({visible:false})\r\n    // public count : ExpressionValue = new ExpressionValue('5');\r\n    // @property({displayName: \"数量\"})\r\n    // public get countStr(): string { return this.count.raw; }\r\n    // public set countStr(value: string) { this.count.raw = value; }\r\n    @property({type: Enum(eWaveCompletion), displayName: \"波次完成条件\"})\r\n    public waveCompletion: eWaveCompletion = eWaveCompletion.SpawnCount;\r\n\r\n    @property({visible:false})\r\n    public waveCompletionParam : ExpressionValue = new ExpressionValue('5');\r\n    @property({displayName: \"完成条件参数\"})\r\n    public get waveCompletionParamStr(): string { return this.waveCompletionParam.raw; }\r\n    public set waveCompletionParamStr(value: string) { this.waveCompletionParam.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public spawnInterval: ExpressionValue = new ExpressionValue('1000');\r\n    @property({displayName: \"出生间隔(ms)\"})\r\n    public get spawnIntervalStr(): string { return this.spawnInterval.raw; }\r\n    public set spawnIntervalStr(value: string) { this.spawnInterval.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public spawnPosX : ExpressionValue = new ExpressionValue('0');\r\n    @property({visible:false})\r\n    public spawnPosY : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: \"出生位置X\"})\r\n    public get spawnPosXStr(): string { return this.spawnPosX.raw; }\r\n    public set spawnPosXStr(value: string) { this.spawnPosX.raw = value; }\r\n    @property({displayName: \"出生位置Y\"})\r\n    public get spawnPosYStr(): string { return this.spawnPosY.raw; }\r\n    public set spawnPosYStr(value: string) { this.spawnPosY.raw = value; }\r\n\r\n    private _spawnPos: Vec2 = new Vec2();\r\n    public get spawnPos(): Vec2 {\r\n        this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());\r\n        return this._spawnPos;\r\n    }\r\n\r\n    @property({visible:false})\r\n    public spawnAngle: ExpressionValue = new ExpressionValue('270');\r\n    @property({displayName: \"出生角度\"})\r\n    public get spawnAngleStr(): string { return this.spawnAngle.raw; }\r\n    public set spawnAngleStr(value: string) { this.spawnAngle.raw = value; }\r\n\r\n    // 以下几个属性走怪物的配置里。\r\n    // @property({visible:false})\r\n    // public spawnSpeed: ExpressionValue = new ExpressionValue('500');\r\n    // @property({displayName: \"出生速度\"})\r\n    // public get spawnSpeedStr(): string { return this.spawnSpeed.raw; }\r\n    // public set spawnSpeedStr(value: string) { this.spawnSpeed.raw = value; }\r\n\r\n    // @property({type: Enum(eWaveAngleType), displayName: \"单位朝向类型\"})\r\n    // public planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;\r\n    // public get isFixedAngleType(): boolean {\r\n    //     return this.planeAngleType == eWaveAngleType.Fixed;\r\n    // }\r\n\r\n    // @property({type: CCInteger, displayName: \"单位朝向\", tooltip: '仅在单位朝向类型为Fixed时有效', \r\n    //     visible() { \r\n    //         //@ts-ignore\r\n    //         return this.planeAngleType == eWaveAngleType.Fixed;\r\n    //     }\r\n    // })\r\n    // public planeAngleFixed: number = 0;\r\n\r\n    // @property({type: CCInteger, displayName: \"单位延迟销毁\", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁'})\r\n    // public delayDestroy: number = 5000;\r\n\r\n    @property({type: [WaveEventGroupData], displayName: '事件组'})\r\n    public eventGroupData: WaveEventGroupData[] = [];\r\n}"]}