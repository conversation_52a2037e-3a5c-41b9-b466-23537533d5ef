import { _decorator, Color, Label, math, Node, Sprite } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';

import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { ResTaskClass } from '../../autogen/luban/schema';
import { DataMgr } from '../../data/DataManager';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
import List from '../common/components/list/List';
import { ProgressPanel } from './components/ProgressPanel';
import { TaskItem } from './components/TaskItem';
const { ccclass, property } = _decorator;


@ccclass('TaskUI')
export class TaskUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/TaskUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.HomeTask }
    public static getUIOption(): UIOpt {
        return { isClickBgHideUI: true }
    }
    @property(Label)
    title: Label | null = null;
    @property(List)
    dailyOrWeekList: List | null = null;
    @property(List)
    achievementList: List | null = null;
    @property(Node)
    tabBtnContainer: Node | null = null;
    @property(ProgressPanel)
    progressPanel: ProgressPanel | null = null;

    private _currentTaskClass: ResTaskClass = ResTaskClass.DAILY_TASK;
    private _tabBtns: ButtonPlus[] = [];
    private _btnTaskClassList: ResTaskClass[] = [ResTaskClass.DAILY_TASK, ResTaskClass.WEEKLY_TASK, ResTaskClass.ACHIEVEMENT];

    protected onLoad(): void {
        this.tabBtnContainer!.children.forEach((c, index) => {
            const btn = c.getComponent(ButtonPlus)!
            this._tabBtns.push(btn);
            btn.addClick(() => {
                this.switchTaskClass(this._btnTaskClassList[index]);
            }, this)
        })
        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this)
    }

    private onLeave() {
        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)
        UIMgr.closeUI(TaskUI)
    }

    async onShow(...args: any[]): Promise<void> {
        this.switchTaskClass(ResTaskClass.DAILY_TASK);
    }

    private switchTaskClass(taskClass: ResTaskClass) {
        this._currentTaskClass = taskClass
        this._tabBtns.forEach((btn, index) => {
            if (taskClass == this._btnTaskClassList[index]) {
                btn.interactable = false;
                btn.getComponent(Sprite)!.color = Color.GRAY;
            } else {
                btn.interactable = true;
                btn.getComponent(Sprite)!.color = math.color("#8DB0E1");
            }
        })
        const taskList = DataMgr.task.getTaskListByClass(taskClass)
        this.dailyOrWeekList!.numItems = taskList.length;
        switch (taskClass) {
            case ResTaskClass.DAILY_TASK:
            case ResTaskClass.WEEKLY_TASK:
                this.dailyOrWeekList!.node.active = true;
                this.achievementList!.node.active = false;
                this.progressPanel!.node.active = true;
                this.progressPanel!.init(taskList);
                this.title!.string = "任务";
                break;
            case ResTaskClass.ACHIEVEMENT:
                this.dailyOrWeekList!.node.active = false;
                this.achievementList!.node.active = true;
                this.title!.string = "成就";
                break;
            default:
                break;
        }
    }

    OnListRender(item: TaskItem, index: number) {
        const taskList = DataMgr.task.getTaskListByClass(this._currentTaskClass);
        item.onRender(taskList[index]);
    }

    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected update(dt: number): void {
    }

}

