import Entity from "db://assets/bundles/common/script/game/ui/base/Entity";
import BaseComp from "db://assets/bundles/common/script/game/ui/base/BaseComp";
import EnemyPlaneBase from "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase";

export class EventGroupComp extends BaseComp {
    plane: EnemyPlaneBase|null = null;

    init(entity: Entity) {
        super.init(entity);

        this.plane = entity as EnemyPlaneBase;
    }
}