import { EventActionBase } from "./IEventAction";
import { IEventGroupContext } from "../EventGroup";

export class BulletActionBase extends EventActionBase {
    // this was intentionally left blank
}

export class BulletAction_Duration extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.duration.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.duration.value = value;
    }
}

export class BulletAction_ElapsedTime extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.elapsedTime;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.elapsedTime = value;
    }
}

export class BulletAction_PosX extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.node.position.x;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        const position = context.bullet!.node.position;
        context.bullet!.node.setPosition(value, position.y);
    }
}

export class BulletAction_PosY extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.node.position.y;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        const position = context.bullet!.node.position;
        context.bullet!.node.setPosition(position.x, value);
    }
}

export class BulletAction_Speed extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.speed.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.speed.value = value;
    }
}

export class BulletAction_SpeedAngle extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.speedAngle.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.speedAngle.value = value;
    }
}

export class BulletAction_Acceleration extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.acceleration.value;
    }
    
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.acceleration.value = value;
    }
}

export class BulletAction_AccelerationAngle extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.accelerationAngle.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.accelerationAngle.value = value;
    }
}

export class BulletAction_Scale extends BulletActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.bullet!.prop.scale.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.scale.value = value;
    }
}

export class BulletAction_ColorR extends BulletActionBase {
    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.bullet!.prop.color.value;
        color.r = value;
        context.bullet!.prop.color.value = color;
    }
}

export class BulletAction_ColorG extends BulletActionBase {
    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.bullet!.prop.color.value;
        color.g = value;
        context.bullet!.prop.color.value = color;
    }
}

export class BulletAction_ColorB extends BulletActionBase {
    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.bullet!.prop.color.value;
        color.b = value;
        context.bullet!.prop.color.value = color;
    }
}

export class BulletAction_FacingMoveDir extends BulletActionBase {
    canLerp(): boolean {
        return false;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.isFacingMoveDir.value = (value === 1);
    }
}

export class BulletAction_Destructive extends BulletActionBase {
    canLerp(): boolean {
        return false;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.isDestructive.value = (value === 1);
    }
}

export class BulletAction_DestructiveOnHit extends BulletActionBase {
    canLerp(): boolean {
        return false;
    }
    
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.bullet!.prop.isDestructiveOnHit.value = (value === 1);
    }
}