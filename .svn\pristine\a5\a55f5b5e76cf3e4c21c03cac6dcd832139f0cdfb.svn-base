import { _decorator, Node, RichText, UITransform, WebView } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass('AnnouncementUI')
export class AnnouncementUI extends BaseUI {
    @property(Node)
    content: Node | null = null;

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;

    @property(RichText)
    richText: RichText | null = null;

    @property(WebView)
    webview: WebView | null = null;

    public static getUrl(): string { return "prefab/ui/AnnouncementUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
    }
    async onOKClick() {
        UIMgr.closeUI(AnnouncementUI);
    }

    async onShow(messgae: string): Promise<void> {
        if (this.webview) {
            // 注入 JavaScript 隐藏滚动条
            this.webview.evaluateJS(`
            document.body.style.overflow = 'hidden';
            document.body.style.width = '700px';
            document.body.style.height = '800px';
        `);

            // 设置 WebView 的节点大小
            const webviewTransform = this.webview.node.getComponent(UITransform);
            if (webviewTransform) {
                webviewTransform.setContentSize(700, 800);
            }
        }

        let wid = this.content!.getComponent(UITransform)!.width;
        let hei = this.richText!.getComponent(UITransform)!.height;
        hei = hei < 800 ? 800 : hei;
        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {

    }
}
