import { _decorator, error, v2, Vec2 } from "cc";
import { EventGroupData } from "./EventGroupData";
import { ExpressionValue } from "./ExpressionValue";
const { ccclass, property } = _decorator;

/**
 * 子弹数据
 * 所有时间相关的，单位都是毫秒(ms)
 */
@ccclass("BulletData")
export class BulletData {
    // 这些数据在子弹表格里
    // @property({displayName: '子弹伤害'})
    // damage : number = 1;                      // 子弹伤害
    // @property({displayName: '子弹Prefab'})
    // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等

    // 是否朝向行进方向
    @property({displayName: '是否朝向行进方向', group: '基础属性'})
    isFacingMoveDir : boolean = false;
    // 是否追踪目标
    @property({displayName: '是否追踪目标', group: '基础属性'})
    isTrackingTarget : boolean = false;
    // 是否离开屏幕自动销毁
    @property({displayName: '是否离屏自动销毁', group: '基础属性'})
    isDestroyOutScreen : boolean = true;
    // 是否可被破坏
    @property({displayName: '是否可被破坏', group: '基础属性'})
    isDestructive : boolean = false; 
    // 命中时是否被销毁
    @property({displayName: '命中时是否被销毁', group: '基础属性'})
    isDestructiveOnHit : boolean = false;

    // 子弹基础缩放
    @property({visible:false})
    public scale : ExpressionValue = new ExpressionValue('1');
    @property({displayName: '子弹基础缩放', group: '基础属性'})
    public get scaleStr(): string { return this.scale.raw; }
    public set scaleStr(value: string) { this.scale.raw = value; }

    // 子弹持续时间(超出后销毁回收)
    @property({visible:false})
    public duration : ExpressionValue = new ExpressionValue('10000');
    @property({displayName: '子弹持续时间', group: '基础属性'})
    public get durationStr(): string { return this.duration.raw; }
    public set durationStr(value: string) { this.duration.raw = value; }

    // 延迟销毁时间
    @property({visible:false})
    public delayDestroy : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '离屏延迟销毁时间', group: '基础属性'})
    public get delayDestroyStr(): string { return this.delayDestroy.raw; }
    public set delayDestroyStr(value: string) { this.delayDestroy.raw = value; }

    // 子弹速度
    @property({visible:false})
    public speed : ExpressionValue = new ExpressionValue('600');
    @property({displayName: '子弹速度', group: '基础属性'})
    public get speedStr(): string { return this.speed.raw; }
    public set speedStr(value: string) { this.speed.raw = value; }

    // 子弹加速度
    @property({visible:false})
    public acceleration : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '子弹加速度', group: '基础属性'})
    public get accelerationStr(): string { return this.acceleration.raw; }
    public set accelerationStr(value: string) { this.acceleration.raw = value; }

    // 加速度方向(角度) 0表示朝向移动方向, 90表示朝向发射方向
    @property({visible:false})
    accelerationAngle : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '加速度方向', group: '基础属性'})
    public get accelerationAngleStr(): string { return this.accelerationAngle.raw; }
    public set accelerationAngleStr(value: string) { this.accelerationAngle.raw = value; }

    @property({type: [EventGroupData], displayName: '事件组', group: '事件组'})
    eventGroupData: EventGroupData[] = [];

    static fromJSON(json: any): BulletData {
        const data = new BulletData();
        if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map(EventGroupData.fromJSON);
        }

        return data;
    }
}
