[{"__type__": "cc.SceneAsset", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 402}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 403}, "_id": "401efd7e-bd20-4537-a13a-f25e6238c2a9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 100}, {"__id__": 332}, {"__id__": 346}, {"__id__": 357}, {"__id__": 372}, {"__id__": 376}], "_active": true, "_components": [{"__id__": 379}, {"__id__": 380}, {"__id__": 381}, {"__id__": 382}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 667, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 99}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46ihxbAfVPT4CTuCqzBBFA"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 93}, {"__id__": 94}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96Y5RnDlZNWZxGmMb0DJdm"}, {"__type__": "cc.Node", "_name": "backgrounds", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 38}, {"__id__": 43}, {"__id__": 48}, {"__id__": 53}, {"__id__": 58}, {"__id__": 63}, {"__id__": 68}, {"__id__": 73}, {"__id__": 78}, {"__id__": 83}, {"__id__": 88}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "00CDwfv/BPoIkOQoDWegaH"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "5f/yly6qpAKawGBNkazz+3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 11}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -67, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 15}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f50+AMiwZGlKhMueoKJk0K", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 1133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 19}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 20}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9aaF72IDpNip95G6J66hvR", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 21}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2333, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 25}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0848NXV+9GqJlu2eaPISaQ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 26}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 3533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 29}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 28}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 30}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8b5iVN7PFOiI/6yb6x0aXD", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 4733, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 34}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 35}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "97axyL2lBK1oF72Xr+MimA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 5933, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 39}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 38}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 40}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "82aoJsmjZEuIQfirCtKY4S", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 41}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 7133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 44}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 43}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 45}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6ezNY6hI9JkZ6CeJABYl6k", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 46}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 8333, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 49}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 48}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 50}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "624cOHNHtAUJ+iPgS5lYme", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 51}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 9533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 54}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 53}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 55}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1fKg2BhTxKmqQD/PIr1kNi", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 56}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 10733, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 59}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 60}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ecF8zmKutGEJNvbmMRzOrd", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 61}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 11933, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 64}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 63}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 65}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3744Be+QNJsqoNG0p/CX6c", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 66}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 13133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 69}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 68}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 70}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1e968qxSlHYoRH/GLmIb6y", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 71}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 14333, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 74}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 75}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3bUoJGi51OMpfa2sv5gm0/", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 76}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 15533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 79}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 78}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 80}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "72tIvvTHNLMoFttLFipa2s", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 81}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 16733, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 84}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 83}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 85}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "17fMFE+hdI94gUGmft/raK", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 86}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 17933, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 89}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 88}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 90}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a6ItutmqJPCJBiS6rbiTn2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 91}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 19133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7egjOOgcxOTbBhrSmQ4fnu"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "18SvvnrM5CuZVOVakpi7Yc"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1M4X5/i9KTLmVTP26as1B"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35Of1AMyFBUJm+XVolYAw0"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "847Cpgnw5MQJbVJAaqz/cz"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_id": "5amxPAAehKs7uoqX2Q55xo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c7cr3UHz9ID5OL86RVgF3A"}, {"__type__": "cc.Node", "_name": "FloorLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 101}, {"__id__": 144}, {"__id__": 152}, {"__id__": 159}, {"__id__": 167}, {"__id__": 315}, {"__id__": 322}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62oMpiqNVJeLfnCzN4mnAI"}, {"__type__": "cc.Node", "_name": "layer_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 102}, {"__id__": 103}, {"__id__": 104}, {"__id__": 134}, {"__id__": 135}], "_active": true, "_components": [{"__id__": 143}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eawFDb9otNpqa1e2EjpeKw"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a2zP2XbbpLX4jSuA2nYIeT"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "01n1bLyJxOW7UtHRQ9BNIQ"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [{"__id__": 105}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56R0wa3tlPepf8FdkkCRgU"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 104}, "_children": [{"__id__": 106}, {"__id__": 120}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 941.26, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f3MfxQgXNDRYu+2cDFGtjv"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 105}, "_prefab": {"__id__": 107}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 106}, "asset": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 108}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "6az5Sn5ZRJZqoPNhgVtgqQ", "prefabRootNode": null, "mountedChildren": [{"__id__": 109}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 110}, "nodes": [{"__id__": 111}, {"__id__": 114}, {"__id__": 117}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 106}, "_prefab": {"__id__": 112}, "__editorExtras__": {"mountedRoot": {"__id__": 106}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "e549918d-a699-468c-9d07-2b1c9dbc1a7a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 113}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "18YXLoxIFF3JSdzgkweCtm", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 106}, "_prefab": {"__id__": 115}, "__editorExtras__": {"mountedRoot": {"__id__": 106}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 114}, "asset": {"__uuid__": "db54204b-56fb-485e-bc0c-66149c7951c3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 116}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e1xadc9ChOppHCliQQTg93", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 106}, "_prefab": {"__id__": 118}, "__editorExtras__": {"mountedRoot": {"__id__": 106}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 117}, "asset": {"__uuid__": "e3ae5be9-b8ca-450f-a8f7-edd149148782", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 119}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f53k6gvj1Pbr1MUgDtlPIA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 105}, "_prefab": {"__id__": 121}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 120}, "asset": {"__uuid__": "23436676-d303-4acf-82f3-4a44d352b69f", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 122}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8d48q9JK5MKqn/InDxtpNX", "prefabRootNode": null, "mountedChildren": [{"__id__": 123}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 124}, "nodes": [{"__id__": 125}, {"__id__": 128}, {"__id__": 131}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 120}, "_prefab": {"__id__": 126}, "__editorExtras__": {"mountedRoot": {"__id__": 120}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 125}, "asset": {"__uuid__": "b37ff484-6217-472b-bf28-09319f8a31bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 127}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a7JyivyRlCT58AyoO+AK4r", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 120}, "_prefab": {"__id__": 129}, "__editorExtras__": {"mountedRoot": {"__id__": 120}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "5b101290-4eae-4e19-b77b-6674f856e767", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 130}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "909a8XL6NGhbVgMSG/00sf", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 120}, "_prefab": {"__id__": 132}, "__editorExtras__": {"mountedRoot": {"__id__": 120}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 131}, "asset": {"__uuid__": "475c7890-0252-4203-8578-e5928cc7c2e8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 133}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "5fHsd22jpFxJELeQPrHNA9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "06oIjwFJBKHLj9zRwBfHQC"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [{"__id__": 136}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a09cDoa/9GDa2yMD00vzZL"}, {"__type__": "cc.Node", "_name": "event", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 135}, "_children": [{"__id__": 137}], "_active": true, "_components": [{"__id__": 140}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 32.154, "y": 1955.817, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "44ArCRo0xIApZwRZmJvJSg"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 136}, "_prefab": {"__id__": 138}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "d5f5e836-9495-4c08-890f-d92cc6696838", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 139}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dbJBswPshNR55jha11qNPD", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "9fa8bQG+gZMmpqlZsjy9rl1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "conditions": [{"__id__": 141}], "triggers": [{"__id__": 142}], "_id": "b0d+VrYhlOT5ZlYGftggsm"}, {"__type__": "LevelEditorCondition"}, {"__type__": "LevelEditorEventTrigger"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": null, "_id": "4bSY45ODNFhJxTZHoNDNEt"}, {"__type__": "cc.Node", "_name": "layer_5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 145}, {"__id__": 146}, {"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_active": true, "_components": [{"__id__": 151}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0fYQEkollLV70ZN8Udj4Ad"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "08A5/kwVpDV6sxkMym4PB9"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 147}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "04xAFYjGlAXZsC1qvdL85E"}, {"__type__": "cc.Node", "_name": "scroll_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 146}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73+8g/YEhPPZqQzrBH8utE"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "256Mettn9GbpMPTxo3jPSC"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50V/wProxJloZc7hRlb+85"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96mQrEiLZF5553B4hEd8b5"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_id": "ecuznu829CxbZqIce+YajP"}, {"__type__": "cc.Node", "_name": "layer_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 153}, {"__id__": 154}, {"__id__": 155}, {"__id__": 156}, {"__id__": 157}], "_active": true, "_components": [{"__id__": 158}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -9750, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62f0nJtpdNaqMWPbFMMOcf"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "95j3ZPGFtFCohA2LMgLlSY"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2eusG3nbZHyps7gZF8wnYO"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03Tmo40vVGZK9x0eOTo/1K"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e5MSJGqxNOyoWqdGP/isFm"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "18VeqUoJtJDqAJO95YrBni"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": null, "_id": "10u4OuDM1IO5cr7+igmepS"}, {"__type__": "cc.Node", "_name": "layer_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 160}, {"__id__": 161}, {"__id__": 163}, {"__id__": 164}, {"__id__": 165}], "_active": true, "_components": [{"__id__": 166}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -8970, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3bLnF6kGxBToBpAys8RJ0Z"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "69wHi2vt5M6q9OKaP42Xqk"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [{"__id__": 162}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "93Va8xjyFFI6aOnTZCta1U"}, {"__type__": "cc.Node", "_name": "scroll_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1q+aLjANBU7e3Yu494jEc"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2dQ4p3n5lDD5Gh5L9gNLKG"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fONd1gZJFBrenOQ2v/ase"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "60s+7WlbFNlK73MIC6XsrF"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": null, "_id": "1a9O3UI1dOhawAHt+B4BuA"}, {"__type__": "cc.Node", "_name": "layer_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 168}, {"__id__": 310}, {"__id__": 311}, {"__id__": 312}, {"__id__": 313}], "_active": true, "_components": [{"__id__": 314}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b0XV7Q4tBLSZ1V3ciyoi3c"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [{"__id__": 169}, {"__id__": 172}, {"__id__": 175}, {"__id__": 178}, {"__id__": 181}, {"__id__": 184}, {"__id__": 187}, {"__id__": 190}, {"__id__": 193}, {"__id__": 196}, {"__id__": 199}, {"__id__": 202}, {"__id__": 205}, {"__id__": 208}, {"__id__": 211}, {"__id__": 214}, {"__id__": 217}, {"__id__": 220}, {"__id__": 223}, {"__id__": 226}, {"__id__": 229}, {"__id__": 232}, {"__id__": 235}, {"__id__": 238}, {"__id__": 241}, {"__id__": 244}, {"__id__": 247}, {"__id__": 250}, {"__id__": 253}, {"__id__": 256}, {"__id__": 259}, {"__id__": 262}, {"__id__": 265}, {"__id__": 268}, {"__id__": 271}, {"__id__": 274}, {"__id__": 277}, {"__id__": 280}, {"__id__": 283}, {"__id__": 286}, {"__id__": 289}, {"__id__": 292}, {"__id__": 295}, {"__id__": 298}, {"__id__": 301}, {"__id__": 304}, {"__id__": 307}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dd/e9doRlGhoWJoj28WoUY"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 170}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 169}, "asset": {"__uuid__": "fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 171}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8dOspYuFdI7bkcXFGhkpqL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 173}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 174}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9cFGMzELpOVrgnLvDT4kDd", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 176}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 175}, "asset": {"__uuid__": "fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 177}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8eSWm/37BNRrVeZRdhyAOY", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 179}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 178}, "asset": {"__uuid__": "fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 180}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "12UYHP32dIAao7LSN7qNnG", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 182}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 181}, "asset": {"__uuid__": "34375de8-2fcc-4139-82d9-ce002f0703df", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 183}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "98Va+jPDNAuLvxUCgmb+pK", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 185}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 184}, "asset": {"__uuid__": "34375de8-2fcc-4139-82d9-ce002f0703df", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 186}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b2f05H7elNNoVtvxQ8uHTy", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 188}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 187}, "asset": {"__uuid__": "f7dcaf81-b030-4ceb-8246-902a9fb758f0", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 189}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7clcmsRMJNWahgMiFAT777", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 191}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 190}, "asset": {"__uuid__": "d07ea99a-7a22-4181-9b53-5df074b47e12", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 192}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dezyUbIvRETLHwwNeInxMi", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 194}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 193}, "asset": {"__uuid__": "6b9b463c-9068-400e-9320-a6855f0e5820", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 195}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7eYqKRadFJoL8fxqkhOtGU", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 197}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 196}, "asset": {"__uuid__": "d3efcc2f-42cd-4820-b60f-79a115d1cefe", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 198}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7btgbDUk5MRbhI0TG6RzFK", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 200}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 199}, "asset": {"__uuid__": "a87bdde7-3c51-4024-801e-9d53f6d4c559", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 201}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1a3/YLgYRNkKGkdeKt0LD2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 203}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 202}, "asset": {"__uuid__": "3f0eabf3-2b26-4403-8582-1818c448726d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 204}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b4dxcvDjVMyrgCuh0yaPSN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 206}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 205}, "asset": {"__uuid__": "f1fc9976-9904-427c-859e-13b13eb2b32e", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 207}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c5z+drFzdFNY4fF7u3abtX", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 209}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 208}, "asset": {"__uuid__": "f1fc9976-9904-427c-859e-13b13eb2b32e", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 210}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dfskS8P5xBub/yjfU3KKp1", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 212}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 211}, "asset": {"__uuid__": "1dda7088-102b-4d67-a176-5f5ddd4611a6", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 213}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "cc02YnvQVDbYHn9OBbytb2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 215}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 214}, "asset": {"__uuid__": "1dda7088-102b-4d67-a176-5f5ddd4611a6", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 216}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "30JP4IG2lLIaWwjW3K1jeU", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 218}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 217}, "asset": {"__uuid__": "a4a203cb-0beb-4a25-8db7-91384aa74193", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 219}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "34GL7S5kVO95R7JTVTXhF0", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 221}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 220}, "asset": {"__uuid__": "1ee4d5da-29ea-472f-bdf1-67ca1e2d2ee1", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 222}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6cLpJv8ctN1qQqWQ8HxGYD", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 224}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 223}, "asset": {"__uuid__": "6fd60140-b3ad-48bb-a5c0-1b6e1cff9930", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 225}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7bO4Gzkk5Im6sW9/tv6eg8", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 227}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 226}, "asset": {"__uuid__": "6fd60140-b3ad-48bb-a5c0-1b6e1cff9930", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 228}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b9dsLwGJxJa57b0jibuzFz", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 230}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6fd60140-b3ad-48bb-a5c0-1b6e1cff9930", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 231}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "aeJxSD7HpCpp/ymUo2qwCL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 233}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 232}, "asset": {"__uuid__": "31b083c6-01d9-41fd-929f-482ae01644b0", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 234}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "92KUVRARNNn68gd0OCMJ9f", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 236}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 235}, "asset": {"__uuid__": "c13f050f-d21e-4a87-b3b2-68c05f496aa9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 237}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d6Q24CSlJAXJ6fWoutsUqZ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 239}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 238}, "asset": {"__uuid__": "c13f050f-d21e-4a87-b3b2-68c05f496aa9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 240}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6dFmBae1RL96V8Rf/uaHif", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 242}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "c13f050f-d21e-4a87-b3b2-68c05f496aa9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 243}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f9xQ2ElRNHMKYFoMWEqRxX", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 245}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 244}, "asset": {"__uuid__": "c13f050f-d21e-4a87-b3b2-68c05f496aa9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 246}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e9UowJvytFkZYm9UCKbRzV", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 248}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 247}, "asset": {"__uuid__": "b3511bf2-bb28-459c-aaba-9a14b83c0d46", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 249}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "91RGS47rJB+4jQZQxd5LZP", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 251}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 250}, "asset": {"__uuid__": "0a47a305-5aa2-4ad6-b8d7-3db3f8ae759d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 252}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "cclNS0SZBMHZ7hPIrPsQtA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 254}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 253}, "asset": {"__uuid__": "9093ff23-4245-43e6-9095-f558379b7375", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 255}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a6mc3lFpRDo4oKUCvx1HVn", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 257}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 256}, "asset": {"__uuid__": "9093ff23-4245-43e6-9095-f558379b7375", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 258}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "760bfQz95JjK89qri9eOKo", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 260}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 259}, "asset": {"__uuid__": "2c6064ed-ef51-482d-a932-c1f0e894a5bb", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 261}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8a/47cefRM+qjnG4ARKzGu", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 263}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 262}, "asset": {"__uuid__": "1070c072-21b8-4c1f-b62b-16b98d72049d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 264}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "92S5L2qFRKYJJIqMYEFczV", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 266}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 265}, "asset": {"__uuid__": "143b8954-1efd-4e6f-b884-72287ff5a79f", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 267}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3aco9byL5EHb7792TP02dN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 269}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 268}, "asset": {"__uuid__": "eabf2306-2d05-4845-9a01-6febc804a1b7", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 270}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "2f+7zyuVJNv4MoH8xDMrYM", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 272}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 271}, "asset": {"__uuid__": "3d2365f8-50af-4528-a96a-961db2fa169b", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 273}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3bkkDihzhMLIUJFPGaFH1H", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 275}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 274}, "asset": {"__uuid__": "53e327fc-c905-46ab-a5ac-1d6b6c19a014", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 276}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e1Ka/+v4VBdYDcPt2nQOOS", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 278}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 277}, "asset": {"__uuid__": "49fd6607-5227-4a63-a61a-6231cb527b7c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 279}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f1YdJjCIVDoL98gJtPgSik", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 281}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 280}, "asset": {"__uuid__": "49fd6607-5227-4a63-a61a-6231cb527b7c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 282}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "19Sb73jSVJ1pFX9Qw3HEaj", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 284}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 283}, "asset": {"__uuid__": "d06534c6-6dd5-40ed-99b2-07b5fd76e8bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 285}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "bcYB9KbeREw4H39OeIcV/9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 287}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "d06534c6-6dd5-40ed-99b2-07b5fd76e8bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 288}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "edZGVbscpGa6j3pfC9WAHR", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 290}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 289}, "asset": {"__uuid__": "d06534c6-6dd5-40ed-99b2-07b5fd76e8bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 291}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b2dUergEVGBaMQAwxlEAJ6", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 293}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 292}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 294}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "fb3mNIAZFHw5hEUSISAXJS", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 296}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 295}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 297}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3e0Mu6eJ1CQ5RAXr3/hO6V", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 299}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 298}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 300}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e4QOgvLGRLCqVI6UkBerhz", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 302}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 301}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 303}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b4IVEtWFVHZo+V9qRugTAH", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 305}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 304}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 306}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8e3KmDE5RJk5y42WXnhQWT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 308}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 307}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 309}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7b6Z+rmFpEPqL6/jv5IpGZ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fezfZAtaJGr7OsnS49ajA6"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c6/7Kr12RCJrfo+WvEzb0z"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37SkGwjZJOjoducGWb6Ri1"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5KBVJRUNLFJiF5P90+e1k"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_id": "ffcEBeE2lCX63nJ3OBet3K"}, {"__type__": "cc.Node", "_name": "layer_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 316}, {"__id__": 317}, {"__id__": 318}, {"__id__": 319}, {"__id__": 320}], "_active": true, "_components": [{"__id__": 321}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c03CleG2dKzI0J/X1b9/sQ"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64k2Wz0iJErbRGUn0mBF1V"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eaFEeJIJZKpoktBtUpU1A9"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c7HuN5su1Fg5Mv3pt2ow5z"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1cIJQMrqRLd6V2qAj4C91a"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9dzvEnQCpLZaNkj6AM9EbR"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": null, "_id": "6a099URu5DD7amljFoitzS"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [{"__id__": 323}, {"__id__": 327}, {"__id__": 328}, {"__id__": 329}, {"__id__": 330}], "_active": true, "_components": [{"__id__": 331}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7800, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ce/YUkd85Fc7Z4sIy99pEj"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [{"__id__": 324}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42M8iRdYFCprfmONQpQnNp"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 323}, "_prefab": {"__id__": 325}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "34375de8-2fcc-4139-82d9-ce002f0703df", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 326}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "2cKrK3AwpDm6dAD9OyjRtH", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4dS04Wx+xBiIsMlSZerPd3"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7dMAdP3qFJH4YlW1peRyUx"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fa/jRQKfVAErnKjDhYaEVA"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "55g5ifRs5IRZfxuhmf5QYU"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 322}, "_enabled": true, "__prefab": null, "_id": "e7orff+d9KU7f1yP11O5r6"}, {"__type__": "cc.Node", "_name": "MainPlane", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 333}, {"__id__": 335}, {"__id__": 340}], "_active": true, "_components": [{"__id__": 345}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5Llji/3pG8ZUsk+/AnCts"}, {"__type__": "cc.Node", "_name": "enemy", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 332}, "_children": [], "_active": true, "_components": [{"__id__": 334}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22ge7J4Q5BcrDOrtqtJWcy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0e4ol/pP9AdL9y81NxK1IW"}, {"__type__": "cc.Node", "_name": "Plane 128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 332}, "_children": [{"__id__": 336}], "_active": true, "_components": [{"__id__": 339}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbYdiDAGtJNILT2mAHeQHg"}, {"__type__": "cc.Node", "_name": "128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 335}, "_children": [], "_active": true, "_components": [{"__id__": 337}, {"__id__": 338}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -146.727, "y": -381.491, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1innlyJpPfbCEAHtzu7nR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 126, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcvUM8lEVG7r+Duly9xTUi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bcbad599-6805-464f-b852-c6330a6cc136@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b9z9YmlO1ORJddzAvkDa26"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "69CWNsBiBF8qLjUsjYBXVD"}, {"__type__": "cc.Node", "_name": "Plane 150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 332}, "_children": [{"__id__": 341}], "_active": true, "_components": [{"__id__": 344}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fSyYswbxJMYFuKxLm+oIa"}, {"__type__": "cc.Node", "_name": "150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 340}, "_children": [], "_active": true, "_components": [{"__id__": 342}, {"__id__": 343}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 49.887, "y": -384.426, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97bvio7axI36wm/98rDd6y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4c8v2GIyBL7p+TWWFkd8E0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "13a76ed5-7bc0-444c-b47f-8beab0558280@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c3kMW+vo1DD4E5mvwzvbme"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dfuqEV4hA5JIX88WPdF7X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 332}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dVZOPnxBB55oxOGHxThGs"}, {"__type__": "cc.Node", "_name": "SkyLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 347}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3kr0ELyxNI6WeWVx+sGNb"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 346}, "_children": [{"__id__": 348}, {"__id__": 352}, {"__id__": 353}, {"__id__": 354}, {"__id__": 355}], "_active": true, "_components": [{"__id__": 356}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -12870, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13VYVgPThFRLpGnyrGL95j"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [{"__id__": 349}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3f+ESEnXFPuYNuwISHMDOj"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 348}, "_prefab": {"__id__": 350}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 349}, "asset": {"__uuid__": "03913dea-9c35-45cb-a101-aa1db7eede2c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 351}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "90dOXuCpdIDpxRTpSShtF0", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3ZjaPl45Nqo76jhYAi89l"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48k+MboiJJloE0kCBHwffA"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c03c5kK5ROgY45DU9Z7CFO"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6crq54GUJOZZJut6OZbaSi"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": null, "_id": "cd1RSofHNEbKZ90vONmYwE"}, {"__type__": "cc.Node", "_name": "DrawNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 358}, {"__id__": 361}, {"__id__": 364}, {"__id__": 367}], "_active": true, "_components": [{"__id__": 370}, {"__id__": 371}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33t5ymWlpLkbT5idbqGQ7X"}, {"__type__": "cc.Node", "_name": "draw<PERSON>iew", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [], "_active": true, "_components": [{"__id__": 359}, {"__id__": 360}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65LbppaIRGz7EVQ8Ktul+X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1Cf74eydN0Yy5qbbxInaR"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "75zN+z8lVGqLYHx2jxa0qd"}, {"__type__": "cc.Node", "_name": "drawMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [], "_active": true, "_components": [{"__id__": 362}, {"__id__": 363}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9btiDQ+VZN95aFsLmdMyGC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9eg7PH/u9Il7l5kqhTVfqD"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "f7nWk9GTBEfo/KF0FlisGD"}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [], "_active": false, "_components": [{"__id__": 365}, {"__id__": 366}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 621.287, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03lbeQRQBBaJmCmQbmnIP1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "0aPFKUoHhJCY99iaVm3K/C"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "时间：11.42", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d7uTBlts9OhJ188cabfoNE"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [], "_active": false, "_components": [{"__id__": 368}, {"__id__": 369}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 454.781, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cW5DvaP9E7bNc04cxSN9/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "d5aSmlRnRDlJwqRXq14CD4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "进度：0.38", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "038VoLC5NCxYX7kf/kDiDH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cewjtt/5IRbxIivL8bKq5"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "23xs7GFb1G6bRSeyAo5w9K"}, {"__type__": "cc.Node", "_name": "GizmoManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 373}, {"__id__": 374}, {"__id__": 375}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "23TQ+5yTNAyK7H6psLGXWg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9jCsZET9L+Ybd8UX16F6R"}, {"__type__": "35b7e0iBnFHtqqvAd1SurM7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "gizmosEnabled": true, "drawInPlayMode": false, "refreshRate": 60, "maxDrawDistance": 2000, "_id": "c15PFCb7JLcrLysr6F+DtZ"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "85LkKkzdVB45KD5PBufbQj"}, {"__type__": "cc.Node", "_name": "WavePreview", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 377}, {"__id__": 378}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97hK/8rTxLfrLJQXLgQpZi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "21ZgnNk/FGEYxocAGfy5xD"}, {"__type__": "e6727UuHORMv49xH6m7Wz2U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_id": "96jNiemDdM35UIwxyvgTsh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "68a25Vb5mhGApMaV59XFjQ0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "progress": 0.39, "_id": "61Fg6c5BVE0I4TKZPyCZ+2"}, {"__type__": "a4bf2J2KGJJV7RbX1jwoQWJ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "levelname": "", "totalTime": 100, "backgroundLayer": {"__id__": 383}, "floorLayers": [{"__id__": 384}, {"__id__": 385}, {"__id__": 386}, {"__id__": 387}, {"__id__": 391}, {"__id__": 392}, {"__id__": 396}], "skyLayers": [{"__id__": 401}], "_id": "f2rN/MimJBS6HE7SikeuYU"}, {"__type__": "LevelEditorBackgroundLayer", "remark": "", "zIndex": 0, "node": {"__id__": 6}, "speed": 200, "scrollLayers": [], "randomLayers": [], "backgrounds": [{"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}]}, {"__type__": "LevelEditorLayer", "remark": "开场衔接层", "zIndex": 0, "node": {"__id__": 322}, "speed": 200, "scrollLayers": [], "randomLayers": []}, {"__type__": "LevelEditorLayer", "remark": "云层衔接", "zIndex": 0, "node": {"__id__": 315}, "speed": 200, "scrollLayers": [], "randomLayers": []}, {"__type__": "LevelEditorLayer", "remark": "Element", "zIndex": 0, "node": {"__id__": 167}, "speed": 200, "scrollLayers": [], "randomLayers": []}, {"__type__": "LevelEditorLayer", "remark": "Fog", "zIndex": 0, "node": {"__id__": 159}, "speed": 230, "scrollLayers": [{"__id__": 388}], "randomLayers": []}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefabs": [], "weight": 100, "splicingMode": 1, "splicingOffsetX": {"__id__": 389}, "splicingOffsetY": {"__id__": 390}}, {"__type__": "LayerRandomRange", "min": 0, "max": 0}, {"__type__": "LayerRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "Clouds", "zIndex": 0, "node": {"__id__": 152}, "speed": 250, "scrollLayers": [], "randomLayers": []}, {"__type__": "LevelEditorLayer", "remark": "Mask", "zIndex": 0, "node": {"__id__": 144}, "speed": 200, "scrollLayers": [{"__id__": 393}], "randomLayers": []}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefabs": [], "weight": 100, "splicingMode": 1, "splicingOffsetX": {"__id__": 394}, "splicingOffsetY": {"__id__": 395}}, {"__type__": "LayerRandomRange", "min": 0, "max": 0}, {"__type__": "LayerRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "random", "zIndex": 0, "node": {"__id__": 101}, "speed": 200, "scrollLayers": [], "randomLayers": [{"__id__": 397}]}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 398}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": [{"__id__": 399}, {"__id__": 400}]}, {"__type__": "LevelEditorRandTerrainUI", "weight": 100, "terrainElement": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorRandTerrainUI", "weight": 100, "terrainElement": {"__uuid__": "23436676-d303-4acf-82f3-4a44d352b69f", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorLayer", "remark": "", "zIndex": 0, "node": {"__id__": 347}, "speed": 330, "scrollLayers": [], "randomLayers": []}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "401efd7e-bd20-4537-a13a-f25e6238c2a9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 38}, {"__id__": 43}, {"__id__": 48}, {"__id__": 53}, {"__id__": 58}, {"__id__": 63}, {"__id__": 68}, {"__id__": 73}, {"__id__": 78}, {"__id__": 83}, {"__id__": 88}, {"__id__": 106}, {"__id__": 111}, {"__id__": 114}, {"__id__": 117}, {"__id__": 120}, {"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 137}, {"__id__": 169}, {"__id__": 172}, {"__id__": 175}, {"__id__": 178}, {"__id__": 181}, {"__id__": 184}, {"__id__": 187}, {"__id__": 190}, {"__id__": 193}, {"__id__": 196}, {"__id__": 199}, {"__id__": 202}, {"__id__": 205}, {"__id__": 208}, {"__id__": 211}, {"__id__": 214}, {"__id__": 217}, {"__id__": 220}, {"__id__": 223}, {"__id__": 226}, {"__id__": 229}, {"__id__": 232}, {"__id__": 235}, {"__id__": 238}, {"__id__": 241}, {"__id__": 244}, {"__id__": 247}, {"__id__": 250}, {"__id__": 253}, {"__id__": 256}, {"__id__": 259}, {"__id__": 262}, {"__id__": 265}, {"__id__": 268}, {"__id__": 271}, {"__id__": 274}, {"__id__": 277}, {"__id__": 280}, {"__id__": 283}, {"__id__": 286}, {"__id__": 289}, {"__id__": 292}, {"__id__": 295}, {"__id__": 298}, {"__id__": 301}, {"__id__": 304}, {"__id__": 307}, {"__id__": 324}, {"__id__": 349}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 404}, "shadows": {"__id__": 405}, "_skybox": {"__id__": 406}, "fog": {"__id__": 407}, "octree": {"__id__": 408}, "skin": {"__id__": 409}, "lightProbeInfo": {"__id__": 410}, "postSettings": {"__id__": 411}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]