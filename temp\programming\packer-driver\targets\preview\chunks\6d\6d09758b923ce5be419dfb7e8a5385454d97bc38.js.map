{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts"], "names": ["EmitterActionBase", "EmitterAction_Active", "EmitterAction_InitialDelay", "EmitterAction_Prewarm", "EmitterAction_PrewarmDuration", "EmitterAction_Duration", "EmitterAction_ElapsedTime", "EmitterAction_Loop", "EmitterAction_LoopInterval", "EmitterAction_EmitInterval", "EmitterAction_PerEmitCount", "EmitterAction_PerEmitInterval", "EmitterAction_PerEmitOffsetX", "EmitterAction_Angle", "EmitterAction_Count", "EmitterAction_BulletDuration", "EmitterAction_BulletDamage", "EmitterAction_BulletSpeed", "EmitterAction_BulletSpeedAngle", "EmitterAction_BulletAcceleration", "EmitterAction_BulletAccelerationAngle", "EmitterAction_BulletScale", "EmitterAction_BulletColorR", "EmitterAction_BulletColorG", "EmitterAction_BulletColorB", "EmitterAction_BulletFacingMoveDir", "EmitterAction_BulletTrackingTarget", "EmitterAction_BulletDestructive", "EmitterAction_BulletDestructiveOnHit", "EventActionBase", "canLerp", "executeInternal", "context", "value", "emitter", "isActive", "resetStartValue", "_startValue", "initialDelay", "isPreWarm", "preWarmDuration", "emitDuration", "totalElapsedTime", "isLoop", "loopInterval", "emitInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "bulletProp", "duration", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "r", "g", "b", "isFacingMoveDir", "isTrackingTarget", "isDestructive", "isDestructiveOnHit"], "mappings": ";;;+CAGaA,iB,EAKAC,oB,EAWAC,0B,EAUAC,qB,EAUAC,6B,EAWAC,sB,EAWAC,yB,EAUAC,kB,EAUAC,0B,EAUAC,0B,EAUAC,0B,EAUAC,6B,EAUAC,4B,EAUAC,mB,EAUAC,mB,EAWAC,4B,EAUAC,0B,EAUAC,yB,EAUAC,8B,EAUAC,gC,EAUAC,qC,EAUAC,yB,EAUAC,0B,EAYAC,0B,EAYAC,0B,EAYAC,iC,EAYAC,kC,EAYAC,+B,EAYAC,oC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtSJC,MAAAA,e,iBAAAA,e;;;;;;;mCAGI7B,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,8CAAgD,CACnD;AADmD,O,GAIvD;;;sCACaC,oB,GAAN,MAAMA,oBAAN,SAAmCD,iBAAnC,CAAqD;AACxD8B,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AAESC,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBC,QAAjB,CAA0BF,KAA1B,GAAkCA,KAAK,KAAK,CAA5C;AACH;;AAPuD,O,GAU5D;;;4CACa/B,0B,GAAN,MAAMA,0BAAN,SAAyCF,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBI,YAAjB,CAA8BL,KAAjD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBI,YAAjB,CAA8BL,KAA9B,GAAsCA,KAAtC;AACH;;AAP6D,O;;uCAUrD9B,qB,GAAN,MAAMA,qBAAN,SAAoCH,iBAApC,CAAsD;AAC/CoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBK,SAAjB,CAA2BN,KAA3B,GAAmC,CAAnC,GAAuC,CAA1D;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBK,SAAjB,CAA2BN,KAA3B,GAAmCA,KAAK,KAAK,CAA7C;AACH;;AAPwD,O;;+CAUhD7B,6B,GAAN,MAAMA,6BAAN,SAA4CJ,iBAA5C,CAA8D;AACvDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBM,eAAjB,CAAiCP,KAApD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBM,eAAjB,CAAiCP,KAAjC,GAAyCA,KAAzC;AACH;;AAPgE,O,GAUrE;;;wCACa5B,sB,GAAN,MAAMA,sBAAN,SAAqCL,iBAArC,CAAuD;AAChDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBO,YAAjB,CAA8BR,KAAjD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBO,YAAjB,CAA8BR,KAA9B,GAAsCA,KAAtC;AACH;;AAPyD,O,GAU9D;;;2CACa3B,yB,GAAN,MAAMA,yBAAN,SAAwCN,iBAAxC,CAA0D;AACnDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBQ,gBAAjB,CAAkCT,KAArD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBQ,gBAAjB,CAAkCT,KAAlC,GAA0CA,KAA1C;AACH;;AAP4D,O;;oCAUpD1B,kB,GAAN,MAAMA,kBAAN,SAAiCP,iBAAjC,CAAmD;AAC5CoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBS,MAAjB,CAAwBV,KAAxB,GAAgC,CAAhC,GAAoC,CAAvD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBS,MAAjB,CAAwBV,KAAxB,GAAgCA,KAAK,KAAK,CAA1C;AACH;;AAPqD,O;;4CAU7CzB,0B,GAAN,MAAMA,0BAAN,SAAyCR,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBU,YAAjB,CAA8BX,KAAjD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBU,YAAjB,CAA8BX,KAA9B,GAAsCA,KAAtC;AACH;;AAP6D,O;;4CAUrDxB,0B,GAAN,MAAMA,0BAAN,SAAyCT,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBW,YAAjB,CAA8BZ,KAAjD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBW,YAAjB,CAA8BZ,KAA9B,GAAsCA,KAAtC;AACH;;AAP6D,O;;4CAUrDvB,0B,GAAN,MAAMA,0BAAN,SAAyCV,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBY,YAAjB,CAA8Bb,KAAjD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBY,YAAjB,CAA8Bb,KAA9B,GAAsCA,KAAtC;AACH;;AAP6D,O;;+CAUrDtB,6B,GAAN,MAAMA,6BAAN,SAA4CX,iBAA5C,CAA8D;AACvDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBa,eAAjB,CAAiCd,KAApD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBa,eAAjB,CAAiCd,KAAjC,GAAyCA,KAAzC;AACH;;AAPgE,O;;8CAUxDrB,4B,GAAN,MAAMA,4BAAN,SAA2CZ,iBAA3C,CAA6D;AACtDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBc,cAAjB,CAAgCf,KAAnD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBc,cAAjB,CAAgCf,KAAhC,GAAwCA,KAAxC;AACH;;AAP+D,O;;qCAUvDpB,mB,GAAN,MAAMA,mBAAN,SAAkCb,iBAAlC,CAAoD;AAC7CoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBe,KAAjB,CAAuBhB,KAA1C;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBe,KAAjB,CAAuBhB,KAAvB,GAA+BA,KAA/B;AACH;;AAPsD,O;;qCAU9CnB,mB,GAAN,MAAMA,mBAAN,SAAkCd,iBAAlC,CAAoD;AAC7CoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBgB,KAAjB,CAAuBjB,KAA1C;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBgB,KAAjB,CAAuBjB,KAAvB,GAA+BA,KAA/B;AACH;;AAPsD,O,GAU3D;;;8CACalB,4B,GAAN,MAAMA,4BAAN,SAA2Cf,iBAA3C,CAA6D;AACtDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BC,QAA5B,CAAqCnB,KAAxD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BC,QAA5B,CAAqCnB,KAArC,GAA6CA,KAA7C;AACH;;AAP+D,O;;4CAUvDjB,0B,GAAN,MAAMA,0BAAN,SAAyChB,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC,CACzD;AACH;;AAESD,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD,CACxE;AACH;;AAP6D,O;;2CAUrDhB,yB,GAAN,MAAMA,yBAAN,SAAwCjB,iBAAxC,CAA0D;AACnDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BE,KAA5B,CAAkCpB,KAArD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BE,KAA5B,CAAkCpB,KAAlC,GAA0CA,KAA1C;AACH;;AAP4D,O;;gDAUpDf,8B,GAAN,MAAMA,8BAAN,SAA6ClB,iBAA7C,CAA+D;AACxDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BG,UAA5B,CAAuCrB,KAA1D;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BG,UAA5B,CAAuCrB,KAAvC,GAA+CA,KAA/C;AACH;;AAPiE,O;;kDAUzDd,gC,GAAN,MAAMA,gCAAN,SAA+CnB,iBAA/C,CAAiE;AAC1DoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BI,YAA5B,CAAyCtB,KAA5D;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BI,YAA5B,CAAyCtB,KAAzC,GAAiDA,KAAjD;AACH;;AAPmE,O;;uDAU3Db,qC,GAAN,MAAMA,qCAAN,SAAoDpB,iBAApD,CAAsE;AAC/DoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BK,iBAA5B,CAA8CvB,KAAjE;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BK,iBAA5B,CAA8CvB,KAA9C,GAAsDA,KAAtD;AACH;;AAPwE,O;;2CAUhEZ,yB,GAAN,MAAMA,yBAAN,SAAwCrB,iBAAxC,CAA0D;AACnDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BM,KAA5B,CAAkCxB,KAArD;AACH;;AAESF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BM,KAA5B,CAAkCxB,KAAlC,GAA0CA,KAA1C;AACH;;AAP4D,O;;4CAUpDX,0B,GAAN,MAAMA,0BAAN,SAAyCtB,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,CAAwC0B,CAA3D;AACH;;AAES5B,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxE,cAAIyB,KAAK,GAAG1B,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAA9C;AACAyB,UAAAA,KAAK,CAACC,CAAN,GAAU1B,KAAV;AACAD,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,GAA0CyB,KAA1C;AACH;;AAT6D,O;;4CAYrDnC,0B,GAAN,MAAMA,0BAAN,SAAyCvB,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,CAAwC2B,CAA3D;AACH;;AAES7B,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxE,cAAIyB,KAAK,GAAG1B,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAA9C;AACAyB,UAAAA,KAAK,CAACE,CAAN,GAAU3B,KAAV;AACAD,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,GAA0CyB,KAA1C;AACH;;AAT6D,O;;4CAYrDlC,0B,GAAN,MAAMA,0BAAN,SAAyCxB,iBAAzC,CAA2D;AACpDoC,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,CAAwC4B,CAA3D;AACH;;AAES9B,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxE,cAAIyB,KAAK,GAAG1B,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAA9C;AACAyB,UAAAA,KAAK,CAACG,CAAN,GAAU5B,KAAV;AACAD,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BO,KAA5B,CAAkCzB,KAAlC,GAA0CyB,KAA1C;AACH;;AAT6D,O;;mDAYrDjC,iC,GAAN,MAAMA,iCAAN,SAAgDzB,iBAAhD,CAAkE;AACrE8B,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSM,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BW,eAA5B,CAA4C7B,KAA5C,GAAoD,CAApD,GAAwD,CAA3E;AACH;;AACSF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BW,eAA5B,CAA4C7B,KAA5C,GAAoDA,KAAK,KAAK,CAA9D;AACH;;AAToE,O;;oDAY5DP,kC,GAAN,MAAMA,kCAAN,SAAiD1B,iBAAjD,CAAmE;AACtE8B,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSM,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BY,gBAA5B,CAA6C9B,KAA7C,GAAqD,CAArD,GAAyD,CAA5E;AACH;;AACSF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4BY,gBAA5B,CAA6C9B,KAA7C,GAAqDA,KAAK,KAAK,CAA/D;AACH;;AATqE,O;;iDAY7DN,+B,GAAN,MAAMA,+BAAN,SAA8C3B,iBAA9C,CAAgE;AACnE8B,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSM,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4Ba,aAA5B,CAA0C/B,KAA1C,GAAkD,CAAlD,GAAsD,CAAzE;AACH;;AACSF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4Ba,aAA5B,CAA0C/B,KAA1C,GAAkDA,KAAK,KAAK,CAA5D;AACH;;AATkE,O;;sDAY1DL,oC,GAAN,MAAMA,oCAAN,SAAmD5B,iBAAnD,CAAqE;AACxE8B,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSM,QAAAA,eAAe,CAACJ,OAAD,EAAoC;AACzD,eAAKK,WAAL,GAAmBL,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4Bc,kBAA5B,CAA+ChC,KAA/C,GAAuD,CAAvD,GAA2D,CAA9E;AACH;;AACSF,QAAAA,eAAe,CAACC,OAAD,EAA8BC,KAA9B,EAAmD;AACxED,UAAAA,OAAO,CAACE,OAAR,CAAiBiB,UAAjB,CAA4Bc,kBAA5B,CAA+ChC,KAA/C,GAAuDA,KAAK,KAAK,CAAjE;AACH;;AATuE,O", "sourcesContent": ["import { EventActionBase } from \"./IEventAction\";\r\nimport { IEventGroupContext } from \"../EventGroup\";\r\n\r\nexport class EmitterActionBase extends EventActionBase {\r\n    // this was intentionally left blank\r\n}\r\n\r\n// 修改发射器启用状态\r\nexport class EmitterAction_Active extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.isActive.value = value === 1;\r\n    }\r\n}\r\n\r\n// 修改发射器初始延迟时间\r\nexport class EmitterAction_InitialDelay extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.initialDelay.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.initialDelay.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Prewarm extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.isPreWarm.value ? 1 : 0;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.isPreWarm.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PrewarmDuration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.preWarmDuration.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.preWarmDuration.value = value;\r\n    }\r\n}\r\n\r\n// 修改发射器持续时间\r\nexport class EmitterAction_Duration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.emitDuration.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.emitDuration.value = value;\r\n    }\r\n}\r\n\r\n// 修改发射器已运行时间\r\nexport class EmitterAction_ElapsedTime extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.totalElapsedTime.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.totalElapsedTime.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Loop extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.isLoop.value ? 1 : 0;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.isLoop.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_LoopInterval extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.loopInterval.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.loopInterval.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_EmitInterval extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.emitInterval.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.emitInterval.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitCount extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.perEmitCount.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.perEmitCount.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitInterval extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.perEmitInterval.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.perEmitInterval.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitOffsetX extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.perEmitOffsetX.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.perEmitOffsetX.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Angle extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.angle.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.angle.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Count extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.count.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.count.value = value;\r\n    }\r\n}\r\n\r\n// 以下是发射器修改子弹属性的部分\r\nexport class EmitterAction_BulletDuration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.duration.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.duration.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDamage extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        // this._startValue = context.emitter!.bulletProp.damage.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        // context.emitter!.bulletProp.damage.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletSpeed extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.speed.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.speed.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletSpeedAngle extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.speedAngle.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.speedAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletAcceleration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.acceleration.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.acceleration.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.accelerationAngle.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.accelerationAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletScale extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.scale.value;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.scale.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorR extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.r;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.r = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorG extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.g;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.g = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorB extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.b;\r\n    }\r\n\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.b = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isFacingMoveDir.value ? 1 : 0;\r\n    }\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isFacingMoveDir.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletTrackingTarget extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isTrackingTarget.value ? 1 : 0;\r\n    }\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isTrackingTarget.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDestructive extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isDestructive.value ? 1 : 0;\r\n    }\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isDestructive.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isDestructiveOnHit.value ? 1 : 0;\r\n    }\r\n    protected executeInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isDestructiveOnHit.value = value === 1;\r\n    }\r\n}"]}