import { PopupUI } from "db://assets/bundles/common/script/ui/common/PopupUI";
import { ToastUI } from "db://assets/bundles/common/script/ui/common/ToastUI";
import { UIMgr } from "./UIMgr";

// 定义回调函数类型
type Callback = () => void;

export class MessageBox {
  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel
  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {
    const cancelHandler = onCancel || (() => { });
    this.show(content, onConfirm, cancelHandler);
  }

  // 只显示确认按钮，调用这个
  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {
    // 统一调用 UIMgr
    UIMgr.openUI(PopupUI, content, onConfirm, onCancel);
  }

  public static toast(content: string) {
    UIMgr.openUI(ToastUI, content);
  }

}
