{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GamePauseUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "GameIns", "ccclass", "property", "GamePauseUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getUIOption", "isClickBgCloseUI", "onLoad", "closeUI", "onShow", "onHide", "onClose", "onDestroy", "onBtnResumeClicked", "gameRuleManager", "gameResume", "onBtnExitClicked", "battleManager", "quitBattle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;6BAGjBO,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb;AAAA;AAAA,4BACwC;AAEhB,eAANC,MAAM,GAAW;AAAE,iBAAO,qBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACnC,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAE9DC,QAAAA,MAAM,GAAS,CAExB;;AACY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcP,WAAd;AACH;;AACW,cAANQ,MAAM,GAAkB,CAC7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,UAAxB;AACA,eAAKP,OAAL;AACH;;AAEDQ,QAAAA,gBAAgB,GAAG;AACf,eAAKR,OAAL;AACA;AAAA;AAAA,kCAAQS,aAAR,CAAsBC,UAAtB;AACH;;AA7BmC,O", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\n\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GameIns } from '../../../game/GameIns';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GamePauseUI')\r\nexport class GamePauseUI extends BaseUI {\r\n\r\n    public static getUrl(): string { return \"ui/game/GamePauseUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(GamePauseUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    onBtnResumeClicked() {\r\n        GameIns.gameRuleManager.gameResume();\r\n        this.closeUI();\r\n    }\r\n\r\n    onBtnExitClicked() {\r\n        this.closeUI();\r\n        GameIns.battleManager.quitBattle();\r\n    }\r\n}\r\n\r\n\r\n"]}