import { EventConditionBase } from "db://assets/bundles/common/script/game/bullet/conditions/IEventCondition";
import { IEventGroupContext, Comparer } from "db://assets/bundles/common/script/game/bullet/EventGroup";
import { EventConditionData, eCompareOp, eConditionOp } from "db://assets/bundles/common/script/game/data/bullet/EventGroupData";
import { WaveEventGroupContext } from "./WaveEventGroup";

export class WaveConditionBase extends EventConditionBase {
    onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this.onLoadInternal(context as WaveEventGroupContext);
    }

    protected onLoadInternal(context: WaveEventGroupContext): void {
    }

    evaluate(context: IEventGroupContext): boolean {
        return this.evaluateInternal(context as WaveEventGroupContext);
    }

    protected evaluateInternal(context: WaveEventGroupContext): boolean {
        return true;
    }
}

export class WaveCondition_PlayerLevel extends WaveConditionBase {
    protected evaluateInternal(context: WaveEventGroupContext): boolean {
        return false;
    }
}

export class WaveCondition_SpawnCount extends WaveConditionBase {
    protected evaluateInternal(context: WaveEventGroupContext): boolean {
        return false;
    }
}