{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts"], "names": ["_decorator", "Label", "Node", "RichText", "UITransform", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "ccclass", "property", "ProtocolType", "TextUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "closeUI", "onShow", "type", "hei", "richText1", "getComponent", "height", "PRIVACY_POLICY", "lblTitle", "string", "node", "active", "richText2", "USER_AGREEMENT", "wid", "content", "width", "setContentSize", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;;AACnCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;8BAElBY,Y,0BAAAA,Y;AAAAA,QAAAA,Y;AAAAA,QAAAA,Y;eAAAA,Y;;;wBAMCC,M,WADZH,OAAO,CAAC,QAAD,C,UAEHC,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACR,QAAD,C,UAGRQ,QAAQ,CAACR,QAAD,C,2BAdb,MACaU,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAgBX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACH;;AACKA,QAAAA,SAAS,GAAG;AAAA;AACd;AAAA;AAAA,gCAAMC,OAAN,CAAcZ,MAAd;AADc;AAEjB;;AAEKa,QAAAA,MAAM,CAACC,IAAD,EAAoC;AAAA;;AAAA;AAC5C,gBAAIC,GAAG,GAAG,KAAI,CAACC,SAAL,CAAgBC,YAAhB,CAA6B1B,WAA7B,EAA2C2B,MAArD;;AACA,gBAAIJ,IAAI,KAAKf,YAAY,CAACoB,cAA1B,EAA0C;AACtC,cAAA,KAAI,CAACC,QAAL,CAAeC,MAAf,GAAwB,QAAxB;AACA,cAAA,KAAI,CAACL,SAAL,CAAgBM,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACA,cAAA,KAAI,CAACC,SAAL,CAAgBF,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACH,aAJD,MAIO,IAAIT,IAAI,KAAKf,YAAY,CAAC0B,cAA1B,EAA0C;AAC7C,cAAA,KAAI,CAACL,QAAL,CAAeC,MAAf,GAAwB,MAAxB;AACAN,cAAAA,GAAG,GAAG,KAAI,CAACS,SAAL,CAAgBP,YAAhB,CAA6B1B,WAA7B,EAA2C2B,MAAjD;AACA,cAAA,KAAI,CAACF,SAAL,CAAgBM,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACA,cAAA,KAAI,CAACC,SAAL,CAAgBF,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACH;;AACD,gBAAIG,GAAG,GAAG,KAAI,CAACC,OAAL,CAAcV,YAAd,CAA2B1B,WAA3B,EAAyCqC,KAAnD;;AACAb,YAAAA,GAAG,GAAGA,GAAG,GAAG,IAAN,GAAa,IAAb,GAAoBA,GAA1B;;AACA,YAAA,KAAI,CAACY,OAAL,CAAcV,YAAd,CAA2B1B,WAA3B,EAAyCsC,cAAzC,CAAwDH,GAAxD,EAA6DX,GAA7D;AAd4C;AAe/C;;AACKe,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAE9B;;AAlD8B,O;;;;;iBAER,I;;;;;;;iBAGE,I;;;;;;;iBAGE,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Label, Node, RichText, UITransform } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum ProtocolType {\r\n    PRIVACY_POLICY = \"PRIVACY_POLICY\",  // 隐私保护协议\r\n    USER_AGREEMENT = \"USER_AGREEMENT\" // 用户协议\r\n}\r\n\r\n@ccclass('TextUI')\r\nexport class TextUI extends BaseUI {\r\n    @property(Node)\r\n    content: Node | null = null;\r\n\r\n    @property(Label)\r\n    lblTitle: Label | null = null;\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n\r\n    @property(RichText)\r\n    richText1: RichText | null = null;\r\n\r\n    @property(RichText)\r\n    richText2: RichText | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/TextUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n    }\r\n    async onOKClick() {\r\n        UIMgr.closeUI(TextUI);\r\n    }\r\n\r\n    async onShow(type: ProtocolType): Promise<void> {\r\n        let hei = this.richText1!.getComponent(UITransform)!.height;\r\n        if (type === ProtocolType.PRIVACY_POLICY) {\r\n            this.lblTitle!.string = \"隐私保护协议\";\r\n            this.richText1!.node.active = true;\r\n            this.richText2!.node.active = false;\r\n        } else if (type === ProtocolType.USER_AGREEMENT) {\r\n            this.lblTitle!.string = \"用户协议\";\r\n            hei = this.richText2!.getComponent(UITransform)!.height;\r\n            this.richText1!.node.active = false;\r\n            this.richText2!.node.active = true;\r\n        }\r\n        let wid = this.content!.getComponent(UITransform)!.width;\r\n        hei = hei < 1100 ? 1100 : hei;\r\n        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n}\r\n"]}