import { EventActionBase, eEventActionStatus } from "db://assets/bundles/common/script/game/bullet/actions/IEventAction";
import { IEventGroupContext, Comparer } from "db://assets/bundles/common/script/game/bullet/EventGroup";
import { WaveEventGroupContext } from "./WaveEventGroup";

export class WaveActionBase extends EventActionBase {
    onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this.resetStartValue(context as WaveEventGroupContext);
        this.resetTargetValue(context as WaveEventGroupContext);
    }

    onExecute(context: IEventGroupContext, dt: number): void {
        this._elapsedTime += dt;

        switch (this._status) {
            case eEventActionStatus.Ready:
                this.onStart(context);
                break;
            case eEventActionStatus.Running:
                if (this._elapsedTime < this._delay) {
                    return;
                }

                if (this._elapsedTime >= this._duration) {
                    this.onComplete(context);
                }
                else if (this.canLerp()) {
                    this.onExecuteInternal(context as WaveEventGroupContext, this.lerpValue(this._startValue, this._targetValue));
                }
                break;
            case eEventActionStatus.Completed:
                return;
        }
    }

    onComplete(context: IEventGroupContext): void {
        this.onExecuteInternal(context as WaveEventGroupContext, this._targetValue);
        this._status = eEventActionStatus.Completed;
    }

    // overload EventActionBase methods
    protected resetStartValue(context: WaveEventGroupContext): void {
    }

    protected resetTargetValue(context: WaveEventGroupContext): void {
    }

    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {
    }
}

export class WaveAction_SpawnInterval extends WaveActionBase {
    protected resetStartValue(context: WaveEventGroupContext): void {
        this._startValue = context.wave!.waveData.spawnInterval.eval();
    }
    
    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {
        context.wave!.waveData.spawnInterval.value = value;
    }
}

export class WaveAction_SpawnAngle extends WaveActionBase {
    protected resetStartValue(context: WaveEventGroupContext): void {
        this._startValue = context.wave!.waveData.spawnAngle.eval();
    }

    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {
        context.wave!.waveData.spawnAngle.value = value;
    }
}

export class WaveAction_PlaneSpeed extends WaveActionBase {
    protected resetStartValue(context: WaveEventGroupContext): void {
        // this._startValue = context.wave!.waveData.planeSpeed.eval();
    }
    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {
        // context.wave!.waveData.planeSpeed.value = value;
    }
}