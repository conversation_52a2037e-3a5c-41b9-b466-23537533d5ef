{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventActions.ts"], "names": ["WaveActionBase", "WaveAction_SpawnInterval", "WaveAction_SpawnAngle", "WaveAction_PlaneSpeed", "EventActionBase", "onLoad", "context", "resetStartValue", "resetTargetValue", "onExecute", "dt", "_elapsedTime", "_delay", "_duration", "onExecuteInternal", "_targetValue", "_isCompleted", "canLerp", "lerp<PERSON><PERSON>ue", "_startValue", "value", "wave", "waveData", "spawnInterval", "eval", "spawnAngle"], "mappings": ";;;+CAIaA,c,EAiCAC,wB,EAUAC,qB,EAUAC,qB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzDJC,MAAAA,e,iBAAAA,e;;;;;;;gCAIIJ,c,GAAN,MAAMA,cAAN;AAAA;AAAA,8CAA6C;AAChDK,QAAAA,MAAM,CAACC,OAAD,EAAoC;AACtC,gBAAMD,MAAN,CAAaC,OAAb;AACA,eAAKC,eAAL,CAAqBD,OAArB;AACA,eAAKE,gBAAL,CAAsBF,OAAtB;AACH;;AAEDG,QAAAA,SAAS,CAACH,OAAD,EAA8BI,EAA9B,EAAgD;AACrD,eAAKC,YAAL,IAAqBD,EAArB;;AACA,cAAI,KAAKC,YAAL,GAAoB,KAAKC,MAA7B,EAAqC;AACjC;AACH;;AAED,cAAI,KAAKD,YAAL,IAAqB,KAAKE,SAA9B,EAAyC;AACrC,iBAAKC,iBAAL,CAAuBR,OAAvB,EAAyD,KAAKS,YAA9D;AACA,iBAAKC,YAAL,GAAoB,IAApB;AACH,WAHD,MAIK,IAAI,KAAKC,OAAL,EAAJ,EAAoB;AACrB,iBAAKH,iBAAL,CAAuBR,OAAvB,EAAyD,KAAKY,SAAL,CAAe,KAAKC,WAApB,EAAiC,KAAKJ,YAAtC,CAAzD;AACH;AACJ,SApB+C,CAsBhD;;;AACUR,QAAAA,eAAe,CAACD,OAAD,EAAuC,CAC/D;;AAESE,QAAAA,gBAAgB,CAACF,OAAD,EAAuC,CAChE;;AAESQ,QAAAA,iBAAiB,CAACR,OAAD,EAAiCc,KAAjC,EAAsD,CAChF;;AA9B+C,O;;0CAiCvCnB,wB,GAAN,MAAMA,wBAAN,SAAuCD,cAAvC,CAAsD;AAC/CO,QAAAA,eAAe,CAACD,OAAD,EAAuC;AAC5D,eAAKa,WAAL,GAAmBb,OAAO,CAACe,IAAR,CAAcC,QAAd,CAAuBC,aAAvB,CAAqCC,IAArC,EAAnB;AACH;;AAESV,QAAAA,iBAAiB,CAACR,OAAD,EAAiCc,KAAjC,EAAsD;AAC7Ed,UAAAA,OAAO,CAACe,IAAR,CAAcC,QAAd,CAAuBC,aAAvB,CAAqCH,KAArC,GAA6CA,KAA7C;AACH;;AAPwD,O;;uCAUhDlB,qB,GAAN,MAAMA,qBAAN,SAAoCF,cAApC,CAAmD;AAC5CO,QAAAA,eAAe,CAACD,OAAD,EAAuC;AAC5D,eAAKa,WAAL,GAAmBb,OAAO,CAACe,IAAR,CAAcC,QAAd,CAAuBG,UAAvB,CAAkCD,IAAlC,EAAnB;AACH;;AAESV,QAAAA,iBAAiB,CAACR,OAAD,EAAiCc,KAAjC,EAAsD;AAC7Ed,UAAAA,OAAO,CAACe,IAAR,CAAcC,QAAd,CAAuBG,UAAvB,CAAkCL,KAAlC,GAA0CA,KAA1C;AACH;;AAPqD,O;;uCAU7CjB,qB,GAAN,MAAMA,qBAAN,SAAoCH,cAApC,CAAmD;AAC5CO,QAAAA,eAAe,CAACD,OAAD,EAAuC,CAC5D;AACH;;AACSQ,QAAAA,iBAAiB,CAACR,OAAD,EAAiCc,KAAjC,EAAsD,CAC7E;AACH;;AANqD,O", "sourcesContent": ["import { EventActionBase } from \"db://assets/bundles/common/script/game/bullet/actions/IEventAction\";\r\nimport { IEventGroupContext, Comparer } from \"db://assets/bundles/common/script/game/bullet/EventGroup\";\r\nimport { WaveEventGroupContext } from \"./WaveEventGroup\";\r\n\r\nexport class WaveActionBase extends EventActionBase {\r\n    onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this.resetStartValue(context as WaveEventGroupContext);\r\n        this.resetTargetValue(context as WaveEventGroupContext);\r\n    }\r\n\r\n    onExecute(context: IEventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        if (this._elapsedTime < this._delay) {\r\n            return;\r\n        }\r\n\r\n        if (this._elapsedTime >= this._duration) {\r\n            this.onExecuteInternal(context as WaveEventGroupContext, this._targetValue);\r\n            this._isCompleted = true;\r\n        }\r\n        else if (this.canLerp()) {\r\n            this.onExecuteInternal(context as WaveEventGroupContext, this.lerpValue(this._startValue, this._targetValue));\r\n        }\r\n    }\r\n\r\n    // overload EventActionBase methods\r\n    protected resetStartValue(context: WaveEventGroupContext): void {\r\n    }\r\n\r\n    protected resetTargetValue(context: WaveEventGroupContext): void {\r\n    }\r\n\r\n    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {\r\n    }\r\n}\r\n\r\nexport class WaveAction_SpawnInterval extends WaveActionBase {\r\n    protected resetStartValue(context: WaveEventGroupContext): void {\r\n        this._startValue = context.wave!.waveData.spawnInterval.eval();\r\n    }\r\n    \r\n    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {\r\n        context.wave!.waveData.spawnInterval.value = value;\r\n    }\r\n}\r\n\r\nexport class WaveAction_SpawnAngle extends WaveActionBase {\r\n    protected resetStartValue(context: WaveEventGroupContext): void {\r\n        this._startValue = context.wave!.waveData.spawnAngle.eval();\r\n    }\r\n\r\n    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {\r\n        context.wave!.waveData.spawnAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class WaveAction_PlaneSpeed extends WaveActionBase {\r\n    protected resetStartValue(context: WaveEventGroupContext): void {\r\n        // this._startValue = context.wave!.waveData.planeSpeed.eval();\r\n    }\r\n    protected onExecuteInternal(context: WaveEventGroupContext, value: number): void {\r\n        // context.wave!.waveData.planeSpeed.value = value;\r\n    }\r\n}"]}