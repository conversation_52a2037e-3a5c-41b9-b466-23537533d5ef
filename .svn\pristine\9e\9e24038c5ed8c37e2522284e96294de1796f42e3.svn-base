import { _decorator } from 'cc';
import EnemyPlaneBase from './EnemyPlaneBase';
import { GameEnum } from '../../../const/GameEnum';
import { EnemyData } from '../../../data/EnemyData';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import { ColliderGroupType } from '../../../collider-system/FCollider';
import FBoxCollider from '../../../collider-system/FBoxCollider';
import { Tools } from '../../../utils/Tools';
import TrackComponent from '../../base/TrackComponent';

const { ccclass, property } = _decorator;

@ccclass('EnemyPlane')
export default class EnemyPlane extends EnemyPlaneBase {

    _roleIndex = 1;//当前形态索引
    _curAction: number = 0;
    
    protected onLoad(): void {
        this.enemy = true
        // this._trackCom = Tools.addScript(this.node, TrackComponent);
    }

    initPlane(data: EnemyData,trackData:any) {
        super.initPlane(data,trackData);
        this._curAction = GameEnum.EnemyAction.Track;
        this._initCollide();
        this.startBattle();
    }

    _initCollide(): void {
        // 添加碰撞组件并初始化
        this.collideComp = this.addComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this);
        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;
        this.colliderEnabled = false;
    }


    startBattle() {
        this.colliderEnabled = true;
        this.updateHpUI();
        // this._trackCom!.setTrackAble(true);
        // this._trackCom!.startTrack();
    }

    updateGameLogic(deltaTime: number) {
        if (!this.isDead) {
            // 更新所有组件
            this.m_comps.forEach((comp) => {
                comp.update(deltaTime);
            });
            // this._trackCom!.updateGameLogic(deltaTime);
            this._moveCom!.tick(deltaTime);
            this.updateAction(deltaTime);
        }
    }

    //1.敌机出现，并且移动到指定位置
    //2.开始变形
    //3.开始追踪主角，追踪过程，会射击
    setAction(action: number) {
        if (this._curAction !== action) {
            this._curAction = action;

            // // 停止射击并启用轨迹
            // this._trackCom!.setTrackAble(true);

            switch (this._curAction) {
                case GameEnum.EnemyAction.Sneak:
                    break;
                case GameEnum.EnemyAction.Track:
                    // 跟踪行为
                    break;
                case GameEnum.EnemyAction.Transform:
                    // 变形行为
                    // this._trackCom!.setTrackAble(false);
                    this._roleIndex++;
                    // this.role!.playAnim("transform", () => {
                    //     this.role!.playAnim("idle" + this._roleIndex);
                    //     this.setAction(GameEnum.EnemyAction.Track);
                    //     this._shootCom!.setNextShootAtOnce();
                    // }) || (
                    this.setAction(GameEnum.EnemyAction.Track)
                    break;
                case GameEnum.EnemyAction.AttackPrepare:
                    // 准备攻击行为
                    this.playAtkAnim(() => {
                        this.setAction(GameEnum.EnemyAction.AttackIng);
                    });
                    break;

                case GameEnum.EnemyAction.AttackIng:
                    // 攻击中行为
                    // this._shootCom!.startShoot();
                    break;
                case GameEnum.EnemyAction.AttackOver:
                    this.setAction(GameEnum.EnemyAction.Track);
                    break;
                default:
                    break;
            }
        }
    }

    /**
    * 播放攻击动画
    */
    playAtkAnim(callback?: Function) {
        // this.plane!.playAnim(`atk${this._roleIndex}`, false, () => {
        //     this.plane!.playAnim(`idle${this._roleIndex}`);
            callback?.();
        // });
    }

    /**每帧都会检测 */
    updateAction(deltaTime: number) {
        // this._shootCom!.setNextAble(false);

        switch (this._curAction) {
            case GameEnum.EnemyAction.Sneak:
                this.colliderEnabled = false;
                break;
            case GameEnum.EnemyAction.Track:
                // this._shootCom!.setNextAble(
                //     (this._trackCom!.isMoving && this._enemyData!.bMoveAttack) ||
                //     (!this._trackCom!.isMoving && this._enemyData!.bStayAttack)
                // );
                break;

            case GameEnum.EnemyAction.Transform:
                break;

            case GameEnum.EnemyAction.AttackPrepare:
            case GameEnum.EnemyAction.AttackIng:
                break;
            case GameEnum.EnemyAction.Leave:
                this.toDie(GameEnum.EnemyDestroyType.TimeOver);
                break;
        }
    }
}