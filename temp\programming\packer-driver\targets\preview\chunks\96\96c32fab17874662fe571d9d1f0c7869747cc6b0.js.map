{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts"], "names": ["_decorator", "assetManager", "Component", "instantiate", "EDITOR", "ccclass", "executeInEditMode", "EmittierTerrain", "_emittier", "_bStart", "_terrainPrefab", "onLoad", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initEmittier", "emittier", "loadAny", "uuid", "err", "prefab", "console", "error", "terrainNode", "setPosition", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "tick", "dt", "update", "play", "bPlay", "onDestroy", "_loadElems"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AACrCC,MAAAA,M,UAAAA,M;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAiCN,U;;iCAI1BO,e,WAFZF,OAAO,CAAC,iBAAD,C,UACPC,iBAAiB,E,+BADlB,MAEaC,eAFb,SAEqCL,SAFrC,CAE+C;AAAA;AAAA;AAAA,eAEnCM,SAFmC,GAEG,IAFH;AAAA,eAGnCC,OAHmC,GAGzB,KAHyB;AAAA,eAInCC,cAJmC,GAIR,EAJQ;AAAA;;AAMjCC,QAAAA,MAAM,GAAS;AACrB,cAAIP,MAAJ,EAAY;AACR,iBAAKQ,IAAL,CAAUC,iBAAV;AACH;AACJ;;AAEMC,QAAAA,YAAY,CAACC,QAAD,EAAoC;AACnD,eAAKH,IAAL,CAAUC,iBAAV;AACA,eAAKL,SAAL,GAAiBO,QAAjB;;AACA,cAAIX,MAAJ,EAAY;AACRH,YAAAA,YAAY,CAACe,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,QAAQ,CAACE;AAAf,aAArB,EAA2C,CAACC,GAAD,EAAaC,MAAb,KAA+B;AACtE,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,8CAAd,EAA8DH,GAA9D;AACA;AACH;;AACD,kBAAII,WAAW,GAAGnB,WAAW,CAACgB,MAAD,CAA7B;AACAG,cAAAA,WAAW,CAACC,WAAZ,CAAwBR,QAAQ,CAACS,QAAT,CAAkBC,CAA1C,EAA6CV,QAAQ,CAACS,QAAT,CAAkBE,CAA/D,EAAkE,CAAlE;AACAJ,cAAAA,WAAW,CAACK,QAAZ,CAAqBZ,QAAQ,CAACa,KAAT,CAAeH,CAApC,EAAuCV,QAAQ,CAACa,KAAT,CAAeF,CAAtD,EAAyD,CAAzD;AACAJ,cAAAA,WAAW,CAACO,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCd,QAAQ,CAACe,QAAhD;AACA,mBAAKlB,IAAL,CAAUmB,QAAV,CAAmBT,WAAnB;AACH,aAVD;AAWH,WAZD,MAYO,CAEN;AACJ;;AAEMU,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKxB,OAAV,EAAmB;AAGtB;;AAESyB,QAAAA,MAAM,GAAS,CAExB;;AAEMC,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAIhC,MAAJ,EAAY;AACR,gBAAIgC,KAAJ,EAAW,CACV;AACJ;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKzB,IAAL,CAAUC,iBAAV;AACH;;AAEOyB,QAAAA,UAAU,GAAS,CAE1B;;AAvD0C,O", "sourcesContent": ["import { _decorator, assetManager, Component, instantiate, Prefab } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { LevelDataEmittier } from '../../leveldata/leveldata';\r\nconst { ccclass, executeInEditMode } = _decorator;\r\n\r\n@ccclass('EmittierTerrain')\r\n@executeInEditMode()\r\nexport class EmittierTerrain extends Component {\r\n\r\n    private _emittier: LevelDataEmittier | null = null;\r\n    private _bStart = false;\r\n    private _terrainPrefab: Prefab[] = [];\r\n\r\n    protected onLoad(): void {\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n        }\r\n    }\r\n\r\n    public initEmittier(emittier: LevelDataEmittier): void {\r\n        this.node.removeAllChildren();\r\n        this._emittier = emittier;\r\n        if (EDITOR) {\r\n            assetManager.loadAny({uuid:emittier.uuid}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"EmittierTerrain initEmittier load prefab err\", err);\r\n                    return\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(emittier.position.x, emittier.position.y, 0);\r\n                terrainNode.setScale(emittier.scale.x, emittier.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, emittier.rotation);\r\n                this.node.addChild(terrainNode);                \r\n            });\r\n        } else {\r\n\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this._bStart) return;\r\n\r\n        \r\n    }\r\n\r\n    protected update(): void {\r\n        \r\n    }\r\n\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _loadElems(): void {\r\n        \r\n    }\r\n}\r\n\r\n"]}