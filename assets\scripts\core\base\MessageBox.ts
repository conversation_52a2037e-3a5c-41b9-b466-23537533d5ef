import { PopupUI } from "db://assets/bundles/common/script/ui/common/PopupUI";
import { ToastUI } from "db://assets/bundles/common/script/ui/common/ToastUI";
import { UIMgr } from "./UIMgr";

// 定义回调函数类型
type Callback = () => void;

export class MessageBox {

  private static _showing: boolean = false;
  private static _content: string[] = []; // 确认回调数组
  private static _confirmCallbacks: (Callback | undefined)[] = []; // 确认回调数组
  private static _cancelCallbacks: (Callback | undefined)[] = [];  // 取消回调数组

  private static async _tryShowNextMessage() {
    if (this._showing || this._content.length === 0) {
      return;
    }
    this._showing = true;
    const content = this._content.shift();
    const onConfirm = this._confirmCallbacks.shift();
    const onCancel = this._cancelCallbacks.shift();
    // 统一调用 UIMgr
    await UIMgr.openUI(PopupUI, content, onConfirm, onCancel);
    let ui = UIMgr.get(PopupUI);
    if (ui) {
      ui.toClose = this.close.bind(this);
    }
  }

  private static async close() {
    await UIMgr.closeUI(PopupUI);
    this._showing = false;
    this._tryShowNextMessage();
  }

  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel
  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {
    const cancelHandler = onCancel || (() => { });
    this.show(content, onConfirm, cancelHandler);
  }

  // 只显示确认按钮，调用这个
  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {
    this._content.push(content);
    this._confirmCallbacks.push(onConfirm);
    this._cancelCallbacks.push(onCancel);
    this._tryShowNextMessage();
  }

  public static toast(content: string) {
    UIMgr.openUI(ToastUI, content);
  }

}
