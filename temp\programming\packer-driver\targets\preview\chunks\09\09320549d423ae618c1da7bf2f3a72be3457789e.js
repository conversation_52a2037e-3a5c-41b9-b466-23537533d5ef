System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventConditionBase, WaveConditionBase, WaveCondition_PlayerLevel, WaveCondition_SpawnCount, _crd;

  function _reportPossibleCrUseOfEventConditionBase(extras) {
    _reporterNs.report("EventConditionBase", "db://assets/bundles/common/script/game/bullet/conditions/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "db://assets/bundles/common/script/game/bullet/EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroupContext(extras) {
    _reporterNs.report("WaveEventGroupContext", "./WaveEventGroup", _context.meta, extras);
  }

  _export({
    WaveConditionBase: void 0,
    WaveCondition_PlayerLevel: void 0,
    WaveCondition_SpawnCount: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      EventConditionBase = _unresolved_2.EventConditionBase;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "14a6aYKIHZOZp+yrBT/BQ8h", "WaveEventConditions", undefined);

      _export("WaveConditionBase", WaveConditionBase = class WaveConditionBase extends (_crd && EventConditionBase === void 0 ? (_reportPossibleCrUseOfEventConditionBase({
        error: Error()
      }), EventConditionBase) : EventConditionBase) {
        onLoad(context) {
          super.onLoad(context);
          this.onLoadInternal(context);
        }

        onLoadInternal(context) {}

        evaluate(context) {
          return this.evaluateInternal(context);
        }

        evaluateInternal(context) {
          return true;
        }

      });

      _export("WaveCondition_PlayerLevel", WaveCondition_PlayerLevel = class WaveCondition_PlayerLevel extends WaveConditionBase {
        evaluateInternal(context) {
          return false;
        }

      });

      _export("WaveCondition_SpawnCount", WaveCondition_SpawnCount = class WaveCondition_SpawnCount extends WaveConditionBase {
        evaluateInternal(context) {
          return false;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=09320549d423ae618c1da7bf2f3a72be3457789e.js.map