2025-9-17 17:25:05 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-9-17 17:25:05 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-9-17 17:25:05 - log: Request namespace: device-list
2025-9-17 17:25:09 - log: emitter-editor extension loaded
2025-9-17 17:25:09 - log: Available methods: [
  'movePlayerUp',
  'movePlayerDown',
  'movePlayerLeft',
  'movePlayerRight'
]
2025-9-17 17:25:11 - info: [PreviewInEditor] 预览环境初始化完毕
2025-9-17 17:25:15 - log: [Scene] meshopt wasm decoder initialized
2025-9-17 17:25:15 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-9-17 17:25:15 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-9-17 17:25:15 - log: [Scene] [PHYSICS]: using builtin.
2025-9-17 17:25:15 - log: [Scene] Cocos Creator v3.8.6
2025-9-17 17:25:19 - log: [Scene] Using custom pipeline: Builtin
2025-9-17 17:25:19 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-9-17 17:25:20 - log: [Scene] LevelEditorBaseUI start.
2025-9-17 17:25:20 - log: [Scene] LevelEditorBaseUI start 62oMpiqNVJeLfnCzN4mnAI
2025-9-17 17:25:20 - log: [Scene] GizmoManager: Registered drawer EmitterGizmo
2025-9-17 17:25:20 - log: [Scene] GizmoManager: Registered drawer LevelEditorEventUIGizmo
2025-9-17 17:25:20 - warn: [Scene] GizmoManager: Drawer EmitterGizmo is already registeredError: [Scene] GizmoManager: Drawer EmitterGizmo is already registered
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.registerDrawer (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:263:21)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:324:26
    at autoRegisterGizmoDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d6/d64c99caf3287de1f11fef52523ad5d812527048.js:33:9)
    at Function.autoRegisterDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:323:68)
    at GizmoManager.onLoad (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:102:24)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50963:132)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at NodeActivator.activateNode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51014:27)
    at Scene._activate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:58797:44)
    at Director.runSceneImmediate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19222:17)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scene\utils.ccc:1:375
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:117467:9)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151169:9)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:25:20 - log: [Scene] LevelUI start
2025-9-17 17:25:20 - log: [Scene] LevelEditorUI set levelPrefabUUID f5e34a8e-0631-42fa-a803-961da4598cdd
2025-9-17 17:25:20 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:25:20 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:25:20 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:22 - log: [Window] panel save level
2025-9-17 17:30:22 - log: [Scene] json:[{"name":"level3","totalTime":40,"backgroundLayer":{"remark":"","zIndex":0,"totalTime":60,"speed":120,"type":1,"terrains":[],"scrolls":[],"dynamics":[],"waves":[],"events":[{"elemID":"belB3mEoJE0LAMijomvx7z","position":{"x":70.242,"y":738.828},"name":"event","conditions":[{"comb":0,"_type":0,"time":0,"targetElemID":""}],"triggers":[{"_type":2,"waveGroup":[{"waveUUID":["d5f5e836-9495-4c08-890f-d92cc6696838"],"weight":50}]}]}],"backgrounds":["8f8d474d-37f5-4010-9299-22846c394020"]},"floorLayers":[],"skyLayers":[{"remark":"","zIndex":0,"totalTime":60,"speed":150,"type":0,"terrains":[{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":-255.56,"y":34.343},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":238.774,"y":766.224},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"475c7890-0252-4203-8578-e5928cc7c2e8","position":{"x":250.742,"y":1906.578},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"f1201696-4b12-4329-91e4-c46bbbe604b8","position":{"x":0,"y":3467.078},"scale":{"x":1,"y":1},"rotation":0}],"scrolls":[],"dynamics":[],"waves":[],"events":[{"elemID":"88QWQYgP9HJrsct9j8GMb2","position":{"x":245.58960338060274,"y":2464.6373618700136},"name":"event","conditions":[{"comb":0,"_type":0,"time":1,"targetElemID":""}],"triggers":[{"_type":2,"waveGroup":[{"waveUUID":["d5f5e836-9495-4c08-890f-d92cc6696838"],"weight":50}]}]}]},{"remark":"","zIndex":0,"totalTime":60,"speed":180,"type":0,"terrains":[{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":-257.467,"y":1433.701},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"475c7890-0252-4203-8578-e5928cc7c2e8","position":{"x":-219.71,"y":2896.176},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"f1201696-4b12-4329-91e4-c46bbbe604b8","position":{"x":0,"y":309.591},"scale":{"x":1,"y":1},"rotation":0}],"scrolls":[],"dynamics":[],"waves":[],"events":[]}]}]
2025-9-17 17:30:22 - log: [Scene] LevelEditorUI save success LevelData {
  name: 'level3',
  totalTime: 40,
  backgroundLayer: LevelDataBackgroundLayer {
    remark: '',
    zIndex: 0,
    totalTime: 60,
    speed: 120,
    type: 1,
    terrains: [],
    scrolls: [],
    dynamics: [],
    waves: [],
    events: [ [LevelDataEvent] ],
    backgrounds: [ '8f8d474d-37f5-4010-9299-22846c394020' ]
  },
  floorLayers: [],
  skyLayers: [
    LevelDataLayer {
      remark: '',
      zIndex: 0,
      totalTime: 60,
      speed: 150,
      type: 0,
      terrains: [Array],
      scrolls: [],
      dynamics: [],
      waves: [],
      events: [Array]
    },
    LevelDataLayer {
      remark: '',
      zIndex: 0,
      totalTime: 60,
      speed: 180,
      type: 0,
      terrains: [Array],
      scrolls: [],
      dynamics: [],
      waves: [],
      events: []
    }
  ]
}
2025-9-17 17:30:22 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:22 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:22 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:22 - log: [Scene] LevelEditorUI reloadLevelPrefab success
2025-9-17 17:30:32 - log: [Scene] json:[{"name":"level3","totalTime":40,"backgroundLayer":{"remark":"","zIndex":0,"totalTime":60,"speed":120,"type":1,"terrains":[],"scrolls":[],"dynamics":[],"waves":[],"events":[{"elemID":"belB3mEoJE0LAMijomvx7z","position":{"x":4.964,"y":791.807},"name":"event","conditions":[{"comb":0,"_type":0,"time":0,"targetElemID":""}],"triggers":[{"_type":2,"waveGroup":[{"waveUUID":["d5f5e836-9495-4c08-890f-d92cc6696838"],"weight":50}]}]}],"backgrounds":["8f8d474d-37f5-4010-9299-22846c394020"]},"floorLayers":[],"skyLayers":[{"remark":"","zIndex":0,"totalTime":60,"speed":150,"type":0,"terrains":[{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":-255.56,"y":34.343},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":238.774,"y":766.224},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"475c7890-0252-4203-8578-e5928cc7c2e8","position":{"x":250.742,"y":1906.578},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"f1201696-4b12-4329-91e4-c46bbbe604b8","position":{"x":0,"y":3467.078},"scale":{"x":1,"y":1},"rotation":0}],"scrolls":[],"dynamics":[],"waves":[],"events":[{"elemID":"88QWQYgP9HJrsct9j8GMb2","position":{"x":245.58960338060274,"y":2464.6373618700136},"name":"event","conditions":[{"comb":0,"_type":0,"time":1,"targetElemID":""}],"triggers":[{"_type":2,"waveGroup":[{"waveUUID":["d5f5e836-9495-4c08-890f-d92cc6696838"],"weight":50}]}]}]},{"remark":"","zIndex":0,"totalTime":60,"speed":180,"type":0,"terrains":[{"uuid":"5b101290-4eae-4e19-b77b-6674f856e767","position":{"x":-257.467,"y":1433.701},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"475c7890-0252-4203-8578-e5928cc7c2e8","position":{"x":-219.71,"y":2896.176},"scale":{"x":1,"y":1},"rotation":0},{"uuid":"f1201696-4b12-4329-91e4-c46bbbe604b8","position":{"x":0,"y":309.591},"scale":{"x":1,"y":1},"rotation":0}],"scrolls":[],"dynamics":[],"waves":[],"events":[]}]}]
2025-9-17 17:30:32 - log: [Scene] LevelEditorUI save success LevelData {
  name: 'level3',
  totalTime: 40,
  backgroundLayer: LevelDataBackgroundLayer {
    remark: '',
    zIndex: 0,
    totalTime: 60,
    speed: 120,
    type: 1,
    terrains: [],
    scrolls: [],
    dynamics: [],
    waves: [],
    events: [ [LevelDataEvent] ],
    backgrounds: [ '8f8d474d-37f5-4010-9299-22846c394020' ]
  },
  floorLayers: [],
  skyLayers: [
    LevelDataLayer {
      remark: '',
      zIndex: 0,
      totalTime: 60,
      speed: 150,
      type: 0,
      terrains: [Array],
      scrolls: [],
      dynamics: [],
      waves: [],
      events: [Array]
    },
    LevelDataLayer {
      remark: '',
      zIndex: 0,
      totalTime: 60,
      speed: 180,
      type: 0,
      terrains: [Array],
      scrolls: [],
      dynamics: [],
      waves: [],
      events: []
    }
  ]
}
2025-9-17 17:30:32 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:32 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:32 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-9-17 17:30:32 - log: [Scene] LevelEditorUI reloadLevelPrefab success
2025-9-17 17:46:22 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-9-17 17:46:22 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-9-17 17:46:22 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-9-17 17:46:22 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-9-17 17:46:23 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-9-17 17:46:23 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-9-17 17:46:23 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 1, 4, 25.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 2, 5, 40.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 3, 6, 50.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 4, 7, 57.14
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 5, 8, 62.50
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 6, 8, 75.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 7, 9, 77.78
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 8, 10, 80.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 9, 11, 81.82
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 10, 11, 90.91
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] ResUpdate load main scene, 11, 11, 100.00
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] CommonEntry initEntry
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Network manager initialized
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] BottomUI onLoad aaaaa
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [DEBUG] DevLoginData localStorage devLoginData:{"servername":"jerry","username":"youngxiang0700","password":"123456"}
2025-9-17 17:46:24 - log: [PreviewInEditor] UI名字: FriendUI
2025-9-17 17:46:24 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] UI名字: MailUI
2025-9-17 17:46:24 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] UI名字: PKUI
2025-9-17 17:46:24 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:46:24 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:46:24 - log: [PreviewInEditor] [2025/09/17 17:46:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] Login dev login
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Connecting to ws://**************:9011
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr WebSocket connected
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang0700#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION seq 0
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Sent message CS_CMD_GET_SESSION, size: 63
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Received message CS_CMD_GET_SESSION {"cmd":"CS_CMD_GET_SESSION","body":{"get_session":{"openid":"youngxiang0700","uin_list":[{"uin":"7205760517782687745","area_id":15,"area_name":"测试区服#15"}]}}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr onGetSession [object Object]
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr onGetSession youngxiang0700:[object Object]
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE {"get_role":{"uin":"0","area_id":0}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE seq 0
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Sent message CS_CMD_GET_ROLE, size: 19
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Received message CS_CMD_GET_ROLE {"cmd":"CS_CMD_GET_ROLE","body":{"get_role":{"base":{"uin":"7205760517782687745","openid":"youngxiang0700","area_id":15,"level":1,"energy":100},"cur_time":1758102385,"client_data":{},"cmd_seq":{"items":[{"cmd":"CS_CMD_UPDATE_ITEM","seq_no":1},{"cmd":"CS_CMD_USE_ITEM","seq_no":1},{"cmd":"CS_CMD_DISCARD_ITEM","seq_no":1},{"cmd":"CS_CMD_EQUIP_COMBINE","seq_no":1},{"cmd":"CS_CMD_EQUIP_SLOT_UPGRADE","seq_no":1}]}}}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST {"game_pvp_get_list":{}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST seq 0
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Sent message CS_CMD_GAME_PVP_GET_LIST, size: 10
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ITEM_LIST
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_UPDATE_ITEM
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_EQUIP_SLOT_INFO
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_INSTALL
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_UNINSTALL
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO {"get_equip_slot_info":{}}
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO seq 0
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Sent message CS_CMD_GET_EQUIP_SLOT_INFO, size: 10
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_MATCH
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_START
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_CANCEL
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_END
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_INFO
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_LIST
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_INFO
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [INFO] NetMgr Registered handler for msgId: CS_CMD_GM
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:26 - log: [PreviewInEditor] [2025/09/17 17:46:26] [DEBUG] NetMgr Received message CS_CMD_GAME_PVP_GET_LIST {"cmd":"CS_CMD_GAME_PVP_GET_LIST","body":{"game_pvp_get_list":{}}}
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr Received message CS_CMD_GET_EQUIP_SLOT_INFO {"cmd":"CS_CMD_GET_EQUIP_SLOT_INFO","body":{"get_equip_slot_info":{"slots":[{"slot_id":1,"level":1,"equip_class":1},{"slot_id":2,"level":1,"equip_class":2},{"slot_id":3,"level":1,"equip_class":2},{"slot_id":4,"level":1,"equip_class":3},{"slot_id":5,"level":1,"equip_class":4}]}}}
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 17:46:27 - warn: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUIError: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.hideUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e8/e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js:396:21)
    at EquipDisplay.onEquipSlotRefresh (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6e/6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js:157:31)
    at EventManager.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d4/d42d10b911d94e8b48693e755ebfb89cc9f5727c.js:45:35)
    at EquipSlots.onGetEquipSlotInfoMsg (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/0d/0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js:89:37)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:409:32
    at Array.forEach (<anonymous>)
    at NetMgr.dispatchMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:407:24)
    at NetMgr.handleMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:386:18)
    at NetMgr.onWebSocketMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:295:18)
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:46:27 - log: [PreviewInEditor] [2025/09/17 17:46:27] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 17:46:29 - log: [PreviewInEditor] [2025/09/17 17:46:29] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:46:29 - log: [PreviewInEditor] [2025/09/17 17:46:29] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:46:29 - log: [PreviewInEditor] [2025/09/17 17:46:29] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 17:46:29 - log: [PreviewInEditor] [2025/09/17 17:46:29] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_KICK_OFF
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun  _levelList  [ 90001, 90002, 90005, 90001, 90001, 90005 ]
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: 0 remainingLevels: 2
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 0
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 1
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 0 offsetY 0
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 loadIndex: 0 _levelUIInfoList push length: 1
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 0 offsetY 0
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-9-17 17:46:29 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-9-17 17:46:29 - log: [PreviewInEditor] GameMapRun 加载关卡: 90002 loadIndex: 1 _levelUIInfoList push length: 2
2025-9-17 17:46:29 - log: [PreviewInEditor] State changed: NONE -> IDLE
2025-9-17 17:46:30 - log: [PreviewInEditor] State changed: IDLE -> ENTER
2025-9-17 17:46:32 - log: [PreviewInEditor] State changed: ENTER -> IDLE
2025-9-17 17:46:44 - log: [PreviewInEditor] EventAction:onLoad: 
2025-9-17 17:46:44 - log: [PreviewInEditor] State changed: IDLE -> MOVE_RIGHT
2025-9-17 17:46:45 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:46:45 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:46:47 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:46:47 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:46:47 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:46:47 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:46:48 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:46:48 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:47:27 - log: [PreviewInEditor] [2025/09/17 17:47:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:47:27 - log: [PreviewInEditor] [2025/09/17 17:47:27] [DEBUG] NetMgr Received message CS_CMD_KICK_OFF {"cmd":"CS_CMD_KICK_OFF","body":{"kick_off":{"reason":"KICKOFF_REASON_SERVER_STOP"}}}
2025-9-17 17:47:27 - warn: [PreviewInEditor] [2025/09/17 17:47:27] [WARN] NetMgr No handler registered for msgId: CS_CMD_KICK_OFFError: [PreviewInEditor] [2025/09/17 17:47:27] [WARN] NetMgr No handler registered for msgId: CS_CMD_KICK_OFF
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at Logger.warn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4db7efb87381f51c2602a8c6e266e53281dfde6.js:306:11)
    at logWarn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4db7efb87381f51c2602a8c6e266e53281dfde6.js:35:12)
    at NetMgr.dispatchMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:428:36)
    at NetMgr.handleMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:386:18)
    at NetMgr.onWebSocketMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:295:18)
2025-9-17 17:47:28 - log: [PreviewInEditor] [2025/09/17 17:47:28] [INFO] NetMgr WebSocket closed: 1006 - 
2025-9-17 17:47:28 - log: [PreviewInEditor] [2025/09/17 17:47:28] [INFO] NetMgr Attempting reconnection 1/5
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [INFO] NetMgr Connecting to ws://**************:9011
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [INFO] NetMgr WebSocket connected
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang0700#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION seq 0
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr Sent message CS_CMD_GET_SESSION, size: 63
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr Received message CS_CMD_GET_SESSION {"cmd":"CS_CMD_GET_SESSION","body":{"get_session":{"openid":"youngxiang0700","uin_list":[{"uin":"7205760517782687745","area_id":15,"area_name":"测试区服#15"}]}}}
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr onGetSession [object Object]
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [INFO] NetMgr onGetSession youngxiang0700:[object Object]
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE {"get_role":{"uin":"0","area_id":0}}
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE seq 0
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr Sent message CS_CMD_GET_ROLE, size: 19
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr Received message CS_CMD_GET_ROLE {"cmd":"CS_CMD_GET_ROLE","body":{"get_role":{"base":{"uin":"7205760517782687745","openid":"youngxiang0700","area_id":15,"level":1,"energy":100},"cur_time":1758102450,"client_data":{},"cmd_seq":{"items":[{"cmd":"CS_CMD_UPDATE_ITEM","seq_no":1},{"cmd":"CS_CMD_USE_ITEM","seq_no":1},{"cmd":"CS_CMD_DISCARD_ITEM","seq_no":1},{"cmd":"CS_CMD_EQUIP_COMBINE","seq_no":1},{"cmd":"CS_CMD_EQUIP_SLOT_UPGRADE","seq_no":1}]}}}}
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST {"game_pvp_get_list":{}}
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST seq 0
2025-9-17 17:47:31 - log: [PreviewInEditor] [2025/09/17 17:47:31] [DEBUG] NetMgr Sent message CS_CMD_GAME_PVP_GET_LIST, size: 10
2025-9-17 17:47:32 - log: [PreviewInEditor] [2025/09/17 17:47:32] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:47:32 - log: [PreviewInEditor] [2025/09/17 17:47:32] [DEBUG] NetMgr Received message CS_CMD_GAME_PVP_GET_LIST {"cmd":"CS_CMD_GAME_PVP_GET_LIST","body":{"game_pvp_get_list":{}}}
2025-9-17 17:48:09 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-9-17 17:48:10 - info: [PreviewInEditor] 预览环境初始化完毕
2025-9-17 17:48:13 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-9-17 17:48:13 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-9-17 17:48:13 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-9-17 17:48:13 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-9-17 17:48:13 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-9-17 17:48:14 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-9-17 17:48:14 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 1, 4, 25.00
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 2, 5, 40.00
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 3, 6, 50.00
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 4, 7, 57.14
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 5, 8, 62.50
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 6, 9, 66.67
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 7, 9, 77.78
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 8, 10, 80.00
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 9, 11, 81.82
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 10, 11, 90.91
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] ResUpdate load main scene, 11, 11, 100.00
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [DEBUG] CommonEntry initEntry
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [INFO] NetMgr Network manager initialized
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-9-17 17:48:14 - log: [PreviewInEditor] [2025/09/17 17:48:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:48:15 - log: [PreviewInEditor] [2025/09/17 17:48:15] [DEBUG] BottomUI onLoad aaaaa
2025-9-17 17:48:15 - log: [PreviewInEditor] [2025/09/17 17:48:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 17:48:15 - log: [PreviewInEditor] [2025/09/17 17:48:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:48:15 - log: [PreviewInEditor] [2025/09/17 17:48:15] [DEBUG] DevLoginData localStorage devLoginData:{"servername":"jerry","username":"youngxiang0700","password":"123456"}
2025-9-17 17:48:15 - log: [PreviewInEditor] UI名字: FriendUI
2025-9-17 17:48:15 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] UI名字: MailUI
2025-9-17 17:48:15 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] UI名字: PKUI
2025-9-17 17:48:15 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 17:48:15 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 17:48:15 - log: [PreviewInEditor] [2025/09/17 17:48:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:48:24 - log: [PreviewInEditor] [2025/09/17 17:48:24] [INFO] Login dev login
2025-9-17 17:48:24 - log: [PreviewInEditor] [2025/09/17 17:48:24] [INFO] NetMgr Connecting to ws://**************:9011
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr WebSocket connected
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang0700#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GET_SESSION, size: 63
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GET_SESSION {"cmd":"CS_CMD_GET_SESSION","body":{"get_session":{"openid":"youngxiang0700","uin_list":[{"uin":"7205760517782687745","area_id":15,"area_name":"测试区服#15"}]}}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr onGetSession [object Object]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr onGetSession youngxiang0700:[object Object]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE {"get_role":{"uin":"0","area_id":0}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GET_ROLE, size: 19
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GET_ROLE {"cmd":"CS_CMD_GET_ROLE","body":{"get_role":{"base":{"uin":"7205760517782687745","openid":"youngxiang0700","area_id":15,"level":1,"energy":100},"cur_time":1758102503,"client_data":{},"cmd_seq":{"items":[{"cmd":"CS_CMD_UPDATE_ITEM","seq_no":1},{"cmd":"CS_CMD_USE_ITEM","seq_no":1},{"cmd":"CS_CMD_DISCARD_ITEM","seq_no":1},{"cmd":"CS_CMD_EQUIP_COMBINE","seq_no":1},{"cmd":"CS_CMD_EQUIP_SLOT_UPGRADE","seq_no":1}]}}}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST {"game_pvp_get_list":{}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GAME_PVP_GET_LIST, size: 10
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ITEM_LIST
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_UPDATE_ITEM
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_EQUIP_SLOT_INFO
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_INSTALL
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_UNINSTALL
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO {"get_equip_slot_info":{}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GET_EQUIP_SLOT_INFO, size: 10
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_MATCH
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_START
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_CANCEL
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_END
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_INFO
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_LIST
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_INFO
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [INFO] NetMgr Registered handler for msgId: CS_CMD_GM
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GAME_PVP_GET_LIST {"cmd":"CS_CMD_GAME_PVP_GET_LIST","body":{"game_pvp_get_list":{}}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GET_EQUIP_SLOT_INFO {"cmd":"CS_CMD_GET_EQUIP_SLOT_INFO","body":{"get_equip_slot_info":{"slots":[{"slot_id":1,"level":1,"equip_class":1},{"slot_id":2,"level":1,"equip_class":2},{"slot_id":3,"level":1,"equip_class":2},{"slot_id":4,"level":1,"equip_class":3},{"slot_id":5,"level":1,"equip_class":4}]}}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 17:48:25 - warn: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUIError: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.hideUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e8/e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js:396:21)
    at EquipDisplay.onEquipSlotRefresh (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6e/6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js:157:31)
    at EventManager.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d4/d42d10b911d94e8b48693e755ebfb89cc9f5727c.js:45:35)
    at EquipSlots.onGetEquipSlotInfoMsg (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/0d/0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js:89:37)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:409:32
    at Array.forEach (<anonymous>)
    at NetMgr.dispatchMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:407:24)
    at NetMgr.handleMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:386:18)
    at NetMgr.onWebSocketMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:295:18)
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 17:48:25 - log: [PreviewInEditor] [2025/09/17 17:48:25] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 17:48:27 - log: [PreviewInEditor] [2025/09/17 17:48:27] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:48:27 - log: [PreviewInEditor] [2025/09/17 17:48:27] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 17:48:27 - log: [PreviewInEditor] [2025/09/17 17:48:27] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 17:48:27 - log: [PreviewInEditor] [2025/09/17 17:48:27] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_KICK_OFF
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun  _levelList  [ 90001, 90001, 90005, 90001, 90002, 90005 ]
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: 0 remainingLevels: 2
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 0
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 0 offsetY 0
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:0
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 loadIndex: 0 _levelUIInfoList push length: 1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 5120 offsetY 0
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:0
2025-9-17 17:48:27 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 loadIndex: 1 _levelUIInfoList push length: 2
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI trigger wave
2025-9-17 17:48:27 - log: [PreviewInEditor] LevelLayerUI trigger wave
2025-9-17 17:48:27 - log: [PreviewInEditor] ybgg addWaveByLevel wave: [object Object]
2025-9-17 17:48:27 - log: [PreviewInEditor] ybgg addWaveByLevel wave: [object Object]
2025-9-17 17:48:27 - log: [PreviewInEditor] State changed: NONE -> IDLE
2025-9-17 17:48:28 - log: [PreviewInEditor] State changed: IDLE -> ENTER
2025-9-17 17:48:30 - log: [PreviewInEditor] State changed: ENTER -> IDLE
2025-9-17 17:48:37 - log: [PreviewInEditor] EventAction:onLoad: 
2025-9-17 17:48:37 - log: [PreviewInEditor] State changed: IDLE -> MOVE_RIGHT
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:38 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:39 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:39 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:39 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:39 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:40 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:40 - error: [PreviewInEditor] Cannot read properties of undefined (reading '_dieWhenOffScreen')TypeError: Cannot read properties of undefined (reading '_dieWhenOffScreen')
    at onBecameInvisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js:170:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:43
    at Array.forEach (<anonymous>)
    at Movable.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:23)
    at Movable.setVisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:306:18)
    at Movable.checkVisibility (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:296:16)
    at Movable.tick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:270:20)
    at EnemyPlane.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js:129:27)
    at EnemyManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js:231:21)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js:310:50)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js:196:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:48:40 - error: [PreviewInEditor] Cannot read properties of undefined (reading '_dieWhenOffScreen')TypeError: Cannot read properties of undefined (reading '_dieWhenOffScreen')
    at onBecameInvisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js:170:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:43
    at Array.forEach (<anonymous>)
    at Movable.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:23)
    at Movable.setVisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:306:18)
    at Movable.checkVisibility (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:296:16)
    at Movable.tick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:270:20)
    at EnemyPlane.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js:129:27)
    at EnemyManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js:231:21)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js:310:50)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js:196:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:48:40 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:40 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:40 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 17:48:40 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 17:48:40 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 17:48:40 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 17:48:40 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:40 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:41 - error: [PreviewInEditor] Cannot read properties of undefined (reading '_dieWhenOffScreen')TypeError: Cannot read properties of undefined (reading '_dieWhenOffScreen')
    at onBecameInvisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js:170:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:43
    at Array.forEach (<anonymous>)
    at Movable.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:23)
    at Movable.setVisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:306:18)
    at Movable.checkVisibility (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:296:16)
    at Movable.tick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:270:20)
    at EnemyPlane.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js:129:27)
    at EnemyManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js:231:21)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js:310:50)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js:196:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:48:41 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:41 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:41 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:41 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:42 - error: [PreviewInEditor] Cannot read properties of undefined (reading '_dieWhenOffScreen')TypeError: Cannot read properties of undefined (reading '_dieWhenOffScreen')
    at onBecameInvisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js:170:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:43
    at Array.forEach (<anonymous>)
    at Movable.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:23)
    at Movable.setVisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:306:18)
    at Movable.checkVisibility (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:296:16)
    at Movable.tick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:270:20)
    at EnemyPlane.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js:129:27)
    at EnemyManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js:231:21)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js:310:50)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js:196:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:42 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:43 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:43 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:43 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:44 - error: [PreviewInEditor] Cannot read properties of undefined (reading '_dieWhenOffScreen')TypeError: Cannot read properties of undefined (reading '_dieWhenOffScreen')
    at onBecameInvisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js:170:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:43
    at Array.forEach (<anonymous>)
    at Movable.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:189:23)
    at Movable.setVisible (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:306:18)
    at Movable.checkVisibility (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:296:16)
    at Movable.tick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9f/9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js:270:20)
    at EnemyPlane.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js:129:27)
    at EnemyManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js:231:21)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js:310:50)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js:196:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-17 17:48:52 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:53 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 17:48:53 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 17:48:59 - info: [PreviewInEditor] 预览环境初始化完毕
2025-9-17 18:03:10 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-9-17 18:03:10 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-9-17 18:03:10 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-9-17 18:03:10 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-9-17 18:03:10 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-9-17 18:03:10 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-9-17 18:03:10 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 1, 4, 25.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 2, 5, 40.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 3, 6, 50.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 4, 7, 57.14
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 5, 8, 62.50
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 6, 8, 75.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 7, 9, 77.78
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 8, 10, 80.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 9, 11, 81.82
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 10, 11, 90.91
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] ResUpdate load main scene, 11, 11, 100.00
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] CommonEntry initEntry
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Network manager initialized
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] BottomUI onLoad aaaaa
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [DEBUG] DevLoginData localStorage devLoginData:{"servername":"jerry","username":"youngxiang0700","password":"123456"}
2025-9-17 18:03:11 - log: [PreviewInEditor] UI名字: FriendUI
2025-9-17 18:03:11 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] UI名字: MailUI
2025-9-17 18:03:11 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] UI名字: PKUI
2025-9-17 18:03:11 - log: [PreviewInEditor] 游戏实际尺寸: 750 x 1335.2601156069366
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5616883116883117 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] 界面设计尺寸: 750 x 1334
2025-9-17 18:03:11 - log: [PreviewInEditor] 宽高比: 0.5622188905547226 = 9 x 16
2025-9-17 18:03:11 - log: [PreviewInEditor] [2025/09/17 18:03:11] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] Login dev login
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Connecting to ws://**************:9011
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr WebSocket connected
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang0700#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_SESSION seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GET_SESSION, size: 63
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GET_SESSION {"cmd":"CS_CMD_GET_SESSION","body":{"get_session":{"openid":"youngxiang0700","uin_list":[{"uin":"7205760517782687745","area_id":15,"area_name":"测试区服#15"}]}}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr onGetSession [object Object]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr onGetSession youngxiang0700:[object Object]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE {"get_role":{"uin":"0","area_id":0}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ROLE seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GET_ROLE, size: 19
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GET_ROLE {"cmd":"CS_CMD_GET_ROLE","body":{"get_role":{"base":{"uin":"7205760517782687745","openid":"youngxiang0700","area_id":15,"level":1,"energy":100},"cur_time":1758103406,"client_data":{},"cmd_seq":{"items":[{"cmd":"CS_CMD_UPDATE_ITEM","seq_no":1},{"cmd":"CS_CMD_USE_ITEM","seq_no":1},{"cmd":"CS_CMD_DISCARD_ITEM","seq_no":1},{"cmd":"CS_CMD_EQUIP_COMBINE","seq_no":1},{"cmd":"CS_CMD_EQUIP_SLOT_UPGRADE","seq_no":1}]}}}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST {"game_pvp_get_list":{}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GAME_PVP_GET_LIST seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GAME_PVP_GET_LIST, size: 10
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ITEM_LIST
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_UPDATE_ITEM
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_EQUIP_SLOT_INFO
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_INSTALL
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_EQUIP_SLOT_UNINSTALL
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO {"get_equip_slot_info":{}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_EQUIP_SLOT_INFO seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GET_EQUIP_SLOT_INFO, size: 10
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_MATCH
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_START
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_CANCEL
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_END
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_INFO
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GAME_PVP_GET_LIST
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_INFO
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_TASK_GET_REWARD
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_GET_ROLE
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [INFO] NetMgr Registered handler for msgId: CS_CMD_GM
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GAME_PVP_GET_LIST {"cmd":"CS_CMD_GAME_PVP_GET_LIST","body":{"game_pvp_get_list":{}}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GET_EQUIP_SLOT_INFO {"cmd":"CS_CMD_GET_EQUIP_SLOT_INFO","body":{"get_equip_slot_info":{"slots":[{"slot_id":1,"level":1,"equip_class":1},{"slot_id":2,"level":1,"equip_class":2},{"slot_id":3,"level":1,"equip_class":2},{"slot_id":4,"level":1,"equip_class":3},{"slot_id":5,"level":1,"equip_class":4}]}}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST {"get_item_list":{}}
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr sendMessage CS_CMD_GET_ITEM_LIST seq 0
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Sent message CS_CMD_GET_ITEM_LIST, size: 9
2025-9-17 18:03:27 - warn: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUIError: [PreviewInEditor] [hideUI] uiMap not found prefab/ui/PlaneEquipInfoUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.hideUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e8/e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js:396:21)
    at EquipDisplay.onEquipSlotRefresh (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6e/6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js:157:31)
    at EventManager.emit (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d4/d42d10b911d94e8b48693e755ebfb89cc9f5727c.js:45:35)
    at EquipSlots.onGetEquipSlotInfoMsg (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/0d/0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js:89:37)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:409:32
    at Array.forEach (<anonymous>)
    at NetMgr.dispatchMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:407:24)
    at NetMgr.handleMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:386:18)
    at NetMgr.onWebSocketMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js:295:18)
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-9-17 18:03:27 - log: [PreviewInEditor] [2025/09/17 18:03:27] [DEBUG] NetMgr Received message CS_CMD_GET_ITEM_LIST {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-9-17 18:03:31 - log: [PreviewInEditor] [2025/09/17 18:03:31] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 18:03:31 - log: [PreviewInEditor] [2025/09/17 18:03:31] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_EQUIP_COMBINE
2025-9-17 18:03:31 - log: [PreviewInEditor] [2025/09/17 18:03:31] [INFO] NetMgr Unregistered handler for msgId: CS_CMD_KICK_OFF
2025-9-17 18:03:31 - log: [PreviewInEditor] [2025/09/17 18:03:31] [INFO] NetMgr Removed empty handler array for msgId: CS_CMD_KICK_OFF
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun  _levelList  [ 90001, 90001, 90005, 90002, 90002, 90005 ]
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: 0 remainingLevels: 2
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 0
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun  level LoadIndex: 1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 0 offsetY 0
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:0
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 loadIndex: 0 _levelUIInfoList push length: 1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _preLevelHeight 5120 offsetY 0
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:1
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI  initByLevelData events:0
2025-9-17 18:03:31 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 loadIndex: 1 _levelUIInfoList push length: 2
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI trigger wave
2025-9-17 18:03:31 - log: [PreviewInEditor] LevelLayerUI trigger wave
2025-9-17 18:03:31 - log: [PreviewInEditor] ybgg addWaveByLevel wave: [object Object]
2025-9-17 18:03:31 - log: [PreviewInEditor] ybgg addWaveByLevel wave: [object Object]
2025-9-17 18:03:31 - log: [PreviewInEditor] State changed: NONE -> IDLE
2025-9-17 18:03:32 - log: [PreviewInEditor] State changed: IDLE -> ENTER
2025-9-17 18:03:34 - log: [PreviewInEditor] State changed: ENTER -> IDLE
2025-9-17 18:03:34 - log: [PreviewInEditor] EventAction:onLoad: 
2025-9-17 18:03:34 - log: [PreviewInEditor] State changed: IDLE -> MOVE_RIGHT
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:35 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:36 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:37 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:37 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:37 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:37 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:37 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-9-17 18:03:38 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-9-17 18:03:39 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:40 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:40 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:40 - log: [PreviewInEditor] State changed: MOVE_RIGHT -> MOVE_LEFT
2025-9-17 18:03:40 - log: [PreviewInEditor] State changed: MOVE_LEFT -> MOVE_RIGHT
2025-9-17 18:03:43 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-9-17 18:03:43 - info: [PreviewInEditor] 预览环境初始化完毕
2025-9-18 09:49:58 - warn: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.Error: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/7c/7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js:348:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
2025-9-18 09:49:59 - warn: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.Error: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/7c/7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js:348:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
2025-9-18 09:50:00 - log: [Scene] GizmoManager: Registered drawer EmitterGizmo
2025-9-18 09:50:00 - log: [Scene] GizmoManager: Registered drawer LevelEditorEventUIGizmo
2025-9-18 09:50:00 - warn: [Scene] GizmoManager: Drawer EmitterGizmo is already registeredError: [Scene] GizmoManager: Drawer EmitterGizmo is already registered
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.registerDrawer (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:263:21)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:324:26
    at autoRegisterGizmoDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d6/d64c99caf3287de1f11fef52523ad5d812527048.js:33:9)
    at Function.autoRegisterDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:323:68)
    at GizmoManager.onLoad (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:102:24)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50963:132)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at NodeActivator.activateNode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51014:27)
    at Scene._activate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:58797:44)
    at Director.runSceneImmediate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19222:17)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scene\utils.ccc:1:375
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:117467:9)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151169:9)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-18 09:50:00 - log: [Scene] reset:  false
2025-9-18 09:50:08 - log: [Scene] reset:  false
2025-9-18 10:24:28 - warn: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.Error: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/7c/7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js:348:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
2025-9-18 10:24:28 - log: [Scene] GizmoManager: Registered drawer EmitterGizmo
2025-9-18 10:24:28 - log: [Scene] GizmoManager: Registered drawer LevelEditorEventUIGizmo
2025-9-18 10:24:28 - warn: [Scene] GizmoManager: Drawer EmitterGizmo is already registeredError: [Scene] GizmoManager: Drawer EmitterGizmo is already registered
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.registerDrawer (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:263:21)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:324:26
    at autoRegisterGizmoDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d6/d64c99caf3287de1f11fef52523ad5d812527048.js:33:9)
    at Function.autoRegisterDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:323:68)
    at GizmoManager.onLoad (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:102:24)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50963:132)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at NodeActivator.activateNode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51014:27)
    at Scene._activate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:58797:44)
    at Director.runSceneImmediate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19222:17)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scene\utils.ccc:1:1367
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:109875:17)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:117467:9)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151169:9)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-18 10:24:28 - log: [Scene] reset:  false
2025-9-18 10:26:23 - warn: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.Error: [Scene] No needs to indicate the 'cc.Boolean' attribute for "LevelEditorEmittierLayerUI.bStart", which its default value is type of Boolean.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/7c/7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js:348:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
2025-9-18 10:26:23 - log: [Scene] GizmoManager: Registered drawer EmitterGizmo
2025-9-18 10:26:23 - log: [Scene] GizmoManager: Registered drawer LevelEditorEventUIGizmo
2025-9-18 10:26:23 - warn: [Scene] GizmoManager: Drawer EmitterGizmo is already registeredError: [Scene] GizmoManager: Drawer EmitterGizmo is already registered
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.registerDrawer (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:263:21)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:324:26
    at autoRegisterGizmoDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d6/d64c99caf3287de1f11fef52523ad5d812527048.js:33:9)
    at Function.autoRegisterDrawers (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:323:68)
    at GizmoManager.onLoad (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js:102:24)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50963:132)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at NodeActivator.activateNode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51014:27)
    at Scene._activate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:58797:44)
    at Director.runSceneImmediate (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19222:17)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scene\utils.ccc:1:1367
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:109875:17)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:117467:9)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151169:9)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-9-18 10:26:23 - log: [Scene] reset:  false
