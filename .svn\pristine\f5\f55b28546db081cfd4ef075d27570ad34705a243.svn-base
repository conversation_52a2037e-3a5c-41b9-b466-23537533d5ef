import { IEventConditionData } from "../../data/bullet/EventGroupData";
import { IEventGroupContext } from "../EventGroup";

// Base interfaces
export interface IEventCondition {
    readonly data: IEventConditionData;

    onLoad(context: IEventGroupContext): void;
    evaluate(context: IEventGroupContext): boolean;
}

export class EventConditionBase implements IEventCondition {
    readonly data: IEventConditionData;

    protected _targetValue: number = 0;

    constructor(data: IEventConditionData) {
        this.data = data;
    }

    onLoad(context: IEventGroupContext): void {
        this._targetValue = this.data.targetValue.eval();
    }

    evaluate(context: IEventGroupContext): boolean {
        // Default implementation (can be overridden)
        return true;
    }
}
