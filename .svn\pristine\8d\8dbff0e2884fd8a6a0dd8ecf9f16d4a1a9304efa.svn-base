import { _decorator, Node } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { PopupUI } from 'db://assets/bundles/common/script/ui/common/PopupUI';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { PKHistoryUI } from './PKHistoryUI';
import { PKRewardIcon } from './PKRewardIcon';
import { MessageBox } from 'db://assets/scripts/core/base/MessageBox';

const { ccclass, property } = _decorator;

@ccclass('PKUI')
export class PKUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnHistory: ButtonPlus | null = null;
    @property(Node)
    friendPk: Node | null = null;
    @property(Node)
    goldCoinPk: Node | null = null;
    @property(Node)
    diamondPk: Node | null = null;
    @property(Node)
    highDiamondPk: Node | null = null;

    public static getUrl(): string { return "prefab/ui/PKUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomePK; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        this.btnHistory!.addClick(this.onHistoryClick, this);
        this.friendPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onFriendPk, this);
        this.goldCoinPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onGoldCoinPk, this);
        this.diamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onDiamondPk, this);
        this.highDiamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onHighDiamondPk, this);

        this.friendPk!.getComponentInChildren(PKRewardIcon)!.setData(888);

        let data = {
            PKRewardIcon: 123,
            PKRewardIcon2: 456,
        };
        this.highDiamondPk!.getComponentsInChildren(PKRewardIcon)!.forEach(icon => {
            const key = icon.node.name as keyof typeof data;
            const value = data[key] ?? 0; // 默认值为 0
            icon.setData(value);
        });
    }
    private onFriendPk() {
        MessageBox.toast("点击了1");
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, { game_pvp_match: { map_id: 1 } });
    }
    private onGoldCoinPk() {
        UIMgr.openUI(PopupUI, "点击了2");
    }
    private onDiamondPk() {
        UIMgr.openUI(PopupUI, "点击了3");
    }
    private onHighDiamondPk() {
        UIMgr.openUI(PopupUI, "点击了4");
    }

    async closeUI() {
        UIMgr.closeUI(PKUI);
        await UIMgr.openUI(HomeUI)
    }
    async onHistoryClick() {
        UIMgr.closeUI(PKUI);
        await UIMgr.openUI(PKHistoryUI)
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }
}


