import { _decorator, Component, Node } from 'cc';
import { MyApp } from '../../../app/MyApp';
import EventManager from '../../../event/EventManager';
import { GameEvent } from '../../event/GameEvent';
const { ccclass, property } = _decorator;

@ccclass('GameInUI')
export class GameInUI extends Component {

    @property(Node)
    tipNode: Node | null = null

    protected onEnable(): void {
        this.tipNode!.active = true;
        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);
    }

    protected onDisable(): void {
        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);
    }

    onEventGameStart() {
        this.tipNode!.active = false;
    }
}


