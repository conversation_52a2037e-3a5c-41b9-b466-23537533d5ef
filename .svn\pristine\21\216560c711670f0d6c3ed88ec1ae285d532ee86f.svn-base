import { _decorator, Enum } from "cc";
import { eEasing } from "../../bullet/Easing";
import { eEventConditionType, eEmitterCondition, eEmitterConditionCn, eBulletCondition, eBulletConditionCn } from "./EventConditionType";
import { eEventActionType, eEmitterAction, eEmitterActionCn, eBulletAction, eBulletActionCn } from "./EventActionType";
import { ExpressionValue } from "./ExpressionValue";
const { ccclass, property } = _decorator;

export enum eConditionOp {
    And, Or
}

export enum eCompareOp {
    Equal, // 等于
    NotEqual, // 不等于
    Greater, // 大于
    Less, // 小于
    GreaterEqual, // 大于等于
    LessEqual, // 小于等于
}

export enum eTargetValueType {
    Absolute = 1, Relative
}

export enum eWrapMode {
    Once, Loop, Pingpong
}

// 数据基类(不含具体的type类型)
export interface IEventConditionData {
    op: eConditionOp;
    compareOp: eCompareOp;
    targetValue: ExpressionValue;
}

export interface IEventActionData {
    // name is only for debugging
    name: string;
    delay: ExpressionValue;
    duration: ExpressionValue;
    transitionDuration: ExpressionValue;
    targetValue: ExpressionValue;
    targetValueType: eTargetValueType;
    wrapMode: eWrapMode;
    easing: eEasing;
}

@ccclass('EventConditionData')
export class EventConditionData implements IEventConditionData {
    @property({ type: Enum(eConditionOp), displayName: '条件关系' })
    public op: eConditionOp = eConditionOp.And;

    @property({visible:false})
    public type: eEventConditionType = eEmitterCondition.Level_Duration;

    @property({ type: Enum(eEmitterConditionCn), displayName: '发射器条件' })
    public get emitterConditionType(): eEmitterConditionCn { return this.type as unknown as eEmitterConditionCn; }
    public set emitterConditionType(value: eEmitterConditionCn) { this.type = value as unknown as eEmitterCondition; }

    @property({ type: Enum(eBulletConditionCn), displayName: '子弹条件' })
    public get bulletConditionType(): eBulletConditionCn { return this.type as unknown as eBulletConditionCn; }
    public set bulletConditionType(value: eBulletConditionCn) { this.type = value as unknown as eBulletCondition; }

    @property({ type: Enum(eCompareOp), displayName: '比较方式' })
    public compareOp: eCompareOp = eCompareOp.Equal;

    // 条件值: 例如持续时间、距离
    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }

    static fromJSON(json: any): EventConditionData {
        const data = new EventConditionData();
        if (json) Object.assign(data, json);
        return data;
    }
}

@ccclass('EventActionData')
export class EventActionData implements IEventActionData {
    @property({ displayName: '行为名称(编辑器下调试用)', editorOnly: true })
    public name: string = "";

    @property({visible:false})
    public type: eEventActionType = eEmitterAction.Emitter_Active;
    
    @property({ type: Enum(eEmitterActionCn), displayName: '发射器操作', group: '发射器' })
    public get emitterActionType(): eEmitterActionCn { return this.type as unknown as eEmitterActionCn; }
    public set emitterActionType(value: eEmitterActionCn) { this.type = value as unknown as eEmitterAction; }

    @property({ type: Enum(eBulletActionCn), displayName: '子弹操作', group: '子弹' })
    public get bulletActionType(): eBulletActionCn { return this.type as unknown as eBulletActionCn; }
    public set bulletActionType(value: eBulletActionCn) { this.type = value as unknown as eBulletAction; }

    @property({visible:false})
    public delay : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '延迟时间', tooltip: '事件触发后，延迟多少毫秒开始执行该行为' })
    public get delayStr(): number { return this.delay.value; }
    public set delayStr(value: number) { this.delay.value = value; }

    // 持续时间: 0表示立即执行
    @property({visible:false})
    public duration : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '持续时间', tooltip: '决定事件自己的生命周期' })
    public get durationStr(): number { return this.duration.value; }
    public set durationStr(value: number) { this.duration.value = value; }

    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }

    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })
    targetValueType: eTargetValueType = eTargetValueType.Absolute;

    @property({visible:false})
    public transitionDuration : ExpressionValue = new ExpressionValue('1000');
    @property({ displayName: '变换到目标值所需时间' })
    public get transitionDurationStr(): number { return this.transitionDuration.value; }
    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }

    @property({ type: Enum(eWrapMode), displayName: '循环模式' })
    wrapMode: eWrapMode = eWrapMode.Once;

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    easing : eEasing = eEasing.Linear;

    static fromJSON(json: any): EventActionData {
        const data = new EventActionData();
        if (json) Object.assign(data, json);
        return data;
    }
}

@ccclass('EventGroupData')
export class EventGroupData {
    @property({ displayName: '事件组名称' })
    public name: string = "";

    // 重复触发次数(默认1,只能触发一次; -1表示循环触发)
    @property({ displayName: '触发次数' })
    public triggerCount: number = 1;

    @property({ type: [EventConditionData], displayName: '条件列表' })
    public conditions: EventConditionData[] = [];

    @property({ type: [EventActionData], displayName: '行为列表' })
    public actions: EventActionData[] = [];

    public static fromJSON(json: any): EventGroupData {
        const data = new EventGroupData();
        if (json) {
            // Extract conditions and actions before Object.assign
            Object.assign(data, json);
            
            data.conditions = (json.conditions || []).map(EventConditionData.fromJSON);
            data.actions = (json.actions || []).map(EventActionData.fromJSON);
        }
        return data;
    }
}
