import { _decorator, instantiate, Node, Prefab, size, UIOpacity, view } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { Plane } from "db://assets/bundles/common/script/ui/Plane";
import { Bullet } from "../../../bullet/Bullet";
import { Emitter } from "../../../bullet/Emitter";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import FCollider, { ColliderGroupType } from "../../../collider-system/FCollider";
import { GameConst } from "../../../const/GameConst";
import GameResourceList from "../../../const/GameResourceList";
import { GameIns } from "../../../GameIns";
import { eEntityTag } from "../../base/Entity";
import EffectLayer from "../../layer/EffectLayer";
import PlaneBase from "../PlaneBase";

const { ccclass, property } = _decorator;


@ccclass("MainPlane")
export class MainPlane extends PlaneBase {

    @property(Node)
    planeParent: Node | null = null;
    @property(Node)
    NodeEmitter: Node | null = null;

    m_moveEnable = true; // 是否允许移动
    emitterComp: Emitter | null = null; // 发射器

    _hurtActTime = 0; // 受伤动画时间
    _hurtActDuration = 0.5; // 受伤动画持续时间

    _planeData: PlaneData | null = null;//飞机数据
    _plane: Plane | null = null;//飞机显示节点
    _fireEnable = true;//是否允许射击

    onLoad() {
        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this, size(40, 40)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.PLAYER;
        this.colliderEnabled = false;
    }

    update(dt: number) {
        dt = dt * GameIns.battleManager.animSpeed;
        this._hurtActTime += dt;
    }

    initPlane(planeData: PlaneData) {
        this._planeData = planeData;

        //加载飞机显示
        let plane = MyApp.planeMgr.getPlane(planeData);
        this._plane = plane.getComponent(Plane);
        this.planeParent?.addChild(plane);

        this.addTag(eEntityTag.Player);

        //设置飞机发射组件
        this.setEmitter();
        // 禁用射击
        this.setFireEnable(false);

        this.maxHp = this._planeData?.getMaxHP();
        this.curHp = this.maxHp;
    }

    setEmitter() {
        //后期根据飞机的数据，加载不同的发送组件预制体
        let path = GameResourceList.EmitterPrefabPath + "Emitter_main_01";
        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {
            let node = instantiate(prefab);
            this.NodeEmitter?.addChild(node);
            node.setPosition(0, 0);

            this.emitterComp = node.getComponent(Emitter);
            this.emitterComp!.setEntity(this);
            this.emitterComp!.setIsActive(this._fireEnable);
        });
    }

    initBattle() {
        this.node.active = true;
        this.colliderEnabled = false;
        this.updateHpUI();
    }

    /**
     * 主飞机入场动画
     */
    planeIn(): void {
        const targetY = -view.getVisibleSize().height * 0.7;
        this.node.setPosition(0, targetY)
        this.setMoveAble(false)
        this.hpNode!.getComponent(UIOpacity)!.opacity = 0;

        this.node.getComponent(UIOpacity)!.opacity = 0;
        this.scheduleOnce(() => {
            this.node.getComponent(UIOpacity)!.opacity = 255;
            this._plane?.onEnter(() => {
                this.hpNode!.getComponent(UIOpacity)!.opacity = 255;
                this.begine();
            });
        }, 0.7);
    }

    /**
     * 碰撞处理
     * @param {Object} collision 碰撞对象
     */
    onCollide(collision: FCollider) {
        let damage = 0;
        if (collision.entity instanceof Bullet) {
            damage = collision.entity!.getAttack();
        }

        if (damage > 0) {
            this.hurt(damage)
        }
    }

    /**
     * 控制飞机移动
     * @param {number} moveX 水平方向的移动量
     * @param {number} moveY 垂直方向的移动量
     */
    onControl(posX: number, posY: number) {
        if (!this.isDead && this.m_moveEnable) {
            let isLeft = posX < this.node.position.x;
            this._plane?.onMoveCommand(isLeft);

            // 限制飞机移动范围
            posX = Math.min(375, posX);
            posX = Math.max(-375, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-GameConst.ViewHeight, posY);
            this.node.setPosition(posX, posY);
        }
    }

    revive() {
        this.node.active = true;
        this.isDead = false;
        this.curHp = this.maxHp; // 恢复满血
        this.updateHpUI(); // 触发血量更新事件
        this.setFireEnable(true);
        this.setMoveAble(true);
        this.colliderEnabled = true;
    }

    getAttack(): number {
        return this._planeData!.getAttack();
    }

    toDie(): boolean {
        if (!super.toDie()) {
            return false;
        }
        this.node.active = false;
        this.setFireEnable(false);
        this.setMoveAble(false);
        GameIns.battleManager.battleDie();
        return true;
    }

    //实现父类的方法
    playHurtAnim() {
        if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0;
            // 显示红屏效果
            EffectLayer.me.showRedScreen();
        }
    }

    setMoveAble(enable: boolean) {
        this.m_moveEnable = enable;
    }

    setFireEnable(enable: boolean) {
        this._fireEnable = enable;
        if (this.emitterComp){
            this.emitterComp!.setIsActive(enable);
        }
    }

    begine(isContinue = false) {
        if (isContinue) {
            this.setFireEnable(true);
            this.setMoveAble(true);
            this.colliderEnabled = true;
        } else {
            GameIns.battleManager.onPlaneIn();
        }
    }

    get attribute(): AttributeData {
        return this._planeData!;
    }

    setAnimSpeed(speed: number) {
        if (this._plane){
            this._plane.setAnimSpeed(speed);
        }
    }
}