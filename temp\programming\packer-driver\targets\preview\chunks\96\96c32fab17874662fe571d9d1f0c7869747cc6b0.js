System.register(["__unresolved_0", "cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, Component, instantiate, EDITOR, _dec, _dec2, _class, _crd, ccclass, executeInEditMode, EmittierTerrain;

  function _reportPossibleCrUseOfLevelDataEmittier(extras) {
    _reporterNs.report("LevelDataEmittier", "../../leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b1dcb5RE6ZN+7KA2ed/t53E", "EmittierTerrain", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Component', 'instantiate', 'Prefab']);

      ({
        ccclass,
        executeInEditMode
      } = _decorator);

      _export("EmittierTerrain", EmittierTerrain = (_dec = ccclass('EmittierTerrain'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class EmittierTerrain extends Component {
        constructor() {
          super(...arguments);
          this._emittier = null;
          this._bStart = false;
          this._terrainPrefab = [];
        }

        onLoad() {
          if (EDITOR) {
            this.node.removeAllChildren();
          }
        }

        initEmittier(emittier) {
          this.node.removeAllChildren();
          this._emittier = emittier;

          if (EDITOR) {
            assetManager.loadAny({
              uuid: emittier.uuid
            }, (err, prefab) => {
              if (err) {
                console.error("EmittierTerrain initEmittier load prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(emittier.position.x, emittier.position.y, 0);
              terrainNode.setScale(emittier.scale.x, emittier.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, emittier.rotation);
              this.node.addChild(terrainNode);
            });
          } else {}
        }

        tick(dt) {
          if (!this._bStart) return;
        }

        update() {}

        play(bPlay) {
          if (EDITOR) {
            if (bPlay) {}
          }
        }

        onDestroy() {
          this.node.removeAllChildren();
        }

        _loadElems() {}

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=96c32fab17874662fe571d9d1f0c7869747cc6b0.js.map