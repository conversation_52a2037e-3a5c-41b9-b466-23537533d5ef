import { _decorator, Label, Node, RichText, UITransform } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';

const { ccclass, property } = _decorator;

export enum ProtocolType {
    PRIVACY_POLICY = "PRIVACY_POLICY",  // 隐私保护协议
    USER_AGREEMENT = "USER_AGREEMENT" // 用户协议
}

@ccclass('TextUI')
export class TextUI extends BaseUI {
    @property(Node)
    content: Node | null = null;

    @property(Label)
    lblTitle: Label | null = null;

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;

    @property(RichText)
    richText1: RichText | null = null;

    @property(RichText)
    richText2: RichText | null = null;

    public static getUrl(): string { return "prefab/ui/TextUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
    }
    async onOKClick() {
        UIMgr.closeUI(TextUI);
    }

    async onShow(type: ProtocolType): Promise<void> {
        let hei = this.richText1!.getComponent(UITransform)!.height;
        if (type == ProtocolType.PRIVACY_POLICY) {
            this.lblTitle!.string = "隐私保护协议";
            this.richText1!.node.active = true;
            this.richText2!.node.active = false;
        } else if (type == ProtocolType.USER_AGREEMENT) {
            this.lblTitle!.string = "用户协议";
            hei = this.richText2!.getComponent(UITransform)!.height;
            this.richText1!.node.active = false;
            this.richText2!.node.active = true;
        }
        let wid = this.content!.getComponent(UITransform)!.width;
        hei = hei < 800 ? 800 : hei;
        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {

    }
}
