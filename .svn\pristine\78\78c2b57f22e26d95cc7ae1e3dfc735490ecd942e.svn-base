import { _decorator, assetManager, JsonAsset } from "cc";
import { IMgr } from '../../../../scripts/core/base/IMgr';
import * as cfg from '../autogen/luban/schema';
import { BundleName } from "../const/BundleConst";
const { ccclass, property } = _decorator;
@ccclass("LubanMgr")
export class LubanMgr extends IMgr {
    private _table: cfg.Tables | null = null;
    private static readonly LUBAN_PATH = 'luban/';

    init(): void {
        super.init();
    }

    load(): Promise<void> {
        return new Promise((resolve, reject) => {
            const bundle = assetManager.bundles.get(BundleName.Luban);
            bundle!.loadDir("./", JsonAsset, (err, assets: JsonAsset[]) => {
                if (err) {
                    reject(err);
                    return;
                }
                var dataMap = new Map<string, JsonAsset>();
                for (let asset of assets) {
                    dataMap.set(asset.name, asset);
                }
                this._table = new cfg.Tables((file: string) => {
                    if (dataMap.has(file)) {
                        return dataMap.get(file)!.json;
                    }
                    console.warn(`LubanMgr: File ${file} not found in loaded assets.`);
                    return null;
                });
                resolve();
            });
        });
    }
    get table(): cfg.Tables {
        return this._table!;
    }
}