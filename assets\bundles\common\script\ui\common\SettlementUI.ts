import { _decorator, Label, ProgressBar, tween } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';
import { StatisticsUI } from './StatisticsUI';

const { ccclass, property } = _decorator;


@ccclass('SettlementUI')
export class SettlementUI extends BaseUI {

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnNext: ButtonPlus | null = null;

    @property(Label)
    lblScore: Label | null = null;
    @property(Label)
    scoreAdd: Label | null = null;
    @property(Label)
    scoreHigh: Label | null = null;

    @property(Label)
    lblLevel: Label | null = null;
    @property(ProgressBar)
    expProBar: ProgressBar | null = null;
    @property(Label)
    lblExp: Label | null = null;

    private _scoreTween: any; // 分数动画的 tween 引用
    private _expTween: any;   // 经验条动画的 tween 引用

    public static getUrl(): string { return "prefab/ui/SettlementUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
        this.btnNext!.addClick(this.onNextClick, this);

        this.scoreAdd!.string = "分数加成 " + 123 + "%";
        this.lblScore!.string = "0";
        this.scoreHigh!.string = "历史最高分 " + 10000;

        const gap = 0.5;
        // 分数动画
        const score = 1000;
        this._scoreTween = tween({ value: 0 })
            .to(gap, { value: score }, {
                onUpdate: (target) => {
                    if (!this.lblScore || target === undefined) return;
                    this.lblScore.string = Math.round(target.value).toString();
                }
            })
            .start();

        const exp = 0.8;
        const finalExp = 10000; // 最终值
        // 经验条动画
        this._expTween = tween(this.expProBar!)
            .to(gap, { progress: exp }, {
                onUpdate: () => {
                    if (!this.expProBar) return;
                    this.lblExp!.string = `${Math.round(finalExp * this.expProBar.progress)}/${finalExp}`;
                }
            })
            .start();

        this.lblLevel!.string = "lv" + 10;

    }
    async onOKClick() {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
        UIMgr.closeUI(SettlementUI);
    }
    async onNextClick() {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
        await UIMgr.openUI(StatisticsUI);
        UIMgr.closeUI(SettlementUI);
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
    }
}
