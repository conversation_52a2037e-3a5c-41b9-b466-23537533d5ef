import { _decorator, Node } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';
import List from './components/list/List';
import { FriendCellUI } from '../friend/FriendCellUI';
import { StatisticsScoreCell } from './StatisticsScoreCell';
import { StatisticsHurtCell } from './StatisticsHurtCell';

const { ccclass, property } = _decorator;


@ccclass('StatisticsUI')
export class StatisticsUI extends BaseUI {

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnNext: ButtonPlus | null = null;

    @property(List)
    list: List | null = null;

    @property(Node)
    cellsNode: Node | null = null;

    cells: StatisticsHurtCell[] = [];

    public static getUrl(): string { return "prefab/ui/StatisticsUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
        this.btnNext!.addClick(this.onNextClick, this);
        this.list!.numItems = 20;

        let types: string[] = ['战机', '装备', '武器', '道具', '其他'];
        let idx: number = 0;
        this.cellsNode!.children.forEach(node => {
            const cell = node.getComponent(StatisticsHurtCell);
            cell?.setType(types[idx++]);
            if (cell !== null) {
                this.cells.push(cell);
            }
        });
    }
    async onOKClick() {
        UIMgr.closeUI(StatisticsUI);
    }
    async onNextClick() {
        UIMgr.closeUI(StatisticsUI);
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {

    }
    onListRender(listItem: Node, row: number) {
        const cell = listItem.getComponent(StatisticsScoreCell);
        if (cell !== null) {

        }
    }
}
