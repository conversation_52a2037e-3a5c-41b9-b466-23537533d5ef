[{"__type__": "cc.Prefab", "_name": "RewardUI", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RewardUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59+HIECUtJRpXOwx7dMpRK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 1, "g": 6, "b": 24, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "931J5TxDVAwIH2UWceIKce"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34zPQ3OBxPbKd0ucz+aWLx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aCQgdu1lGiYjmQfCDBtLM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 17}], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 486.849, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46SOMw7ihAcqvhLf21p2I4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "恭喜获得", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1deY+R1K9CTq8hHYGjJK9i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85WxCV61NOAZno8cs05oG+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ButtonGet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 26}, {"__id__": 28}], "_prefab": {"__id__": 30}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -474.366, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31+lwMOWZL67yE/ry7PAMK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "领取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4ZmiSWy9P07bKLjyokRgo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53GkRkmjdCIpp1T98M1ASt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 25}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7lWvuCgtFXIgIMRFfOqzs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 27}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2EWPRIalHdIGGiKn7um5d"}, {"__type__": "13f7fOXLHFLK6LXmwAmgjNH", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 29}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "clickDefZoomScale": true, "audioUrl": "", "_N$string": "", "openContinuous": true, "continuousTime": 0.5, "openLongPress": false, "longPressTime": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecZQ4CC0pA2rsbVRJlgy7M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2IPqj6CJO/Zif1lbeiVTo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 1134}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9f1/GtdahHCZXtE62ob78F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17DqUqvMtBxq2l5ClfzmQT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "NodeReward", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 35}, {"__id__": 43}, {"__id__": 51}, {"__id__": 59}, {"__id__": 67}, {"__id__": 75}, {"__id__": 83}, {"__id__": 91}, {"__id__": 99}, {"__id__": 107}, {"__id__": 115}, {"__id__": 123}, {"__id__": 131}, {"__id__": 139}, {"__id__": 147}, {"__id__": 155}], "_active": true, "_components": [{"__id__": 163}, {"__id__": 165}], "_prefab": {"__id__": 167}, "_lpos": {"__type__": "cc.Vec3", "x": 2.123, "y": 84.975, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 36}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 35}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 37}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "05boGq4ItGw7hmeCE5RZyZ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 38}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_name"], "value": "ItemIcon1"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -250, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 44}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 43}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 45}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2douIdjKBPRoAKphOxk2YN", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 46}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_name"], "value": "ItemIcon2"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -150, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 52}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 53}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bfViZqV3dBI4beOcZtaQDY", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 54}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_name"], "value": "ItemIcon3"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -50, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 60}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 61}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "85fCQFKuFPR7gyx+4XqgaU", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 62}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_name"], "value": "ItemIcon4"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 50, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 68}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 67}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 69}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "86eFE+E/hIPYYXcJSMy1dH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 70}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_name"], "value": "ItemIcon5"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 150, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 76}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 75}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 77}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e9rhamp6FAObBwdNpVthqO", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 78}, {"__id__": 80}, {"__id__": 81}, {"__id__": 82}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_name"], "value": "ItemIcon6"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 250, "y": 250, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 84}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 83}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 85}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "31bhgHwEVFEKehg+Ut31si", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 86}, {"__id__": 88}, {"__id__": 89}, {"__id__": 90}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_name"], "value": "ItemIcon7"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -250, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 92}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 91}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 93}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "58aHmpZddBuZbjj76V4rTK", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 94}, {"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_name"], "value": "ItemIcon8"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -150, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 100}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 99}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 101}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f4vWzPcmBJlZlMTiIwrpzX", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 102}, {"__id__": 104}, {"__id__": 105}, {"__id__": 106}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_name"], "value": "ItemIcon9"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -50, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 108}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 107}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 109}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "48HBMxiVtCHbdFkKNCls/2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 110}, {"__id__": 112}, {"__id__": 113}, {"__id__": 114}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_name"], "value": "ItemIcon10"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 50, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 116}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 117}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c9kS2hZEJGcYaksopzGvnV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 118}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_name"], "value": "ItemIcon11"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 150, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 124}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 123}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 125}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "89iJf0UY9J4ohyzP+qRycQ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 126}, {"__id__": 128}, {"__id__": 129}, {"__id__": 130}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_name"], "value": "ItemIcon12"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 250, "y": 150, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 132}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 131}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 133}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "6ey6WxaitNH42AGwvyCiTf", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 134}, {"__id__": 136}, {"__id__": 137}, {"__id__": 138}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_name"], "value": "ItemIcon13"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -250, "y": 50, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 140}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 139}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 141}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "3cyOihNLVOtofHaglSGC/X", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 142}, {"__id__": 144}, {"__id__": 145}, {"__id__": 146}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_name"], "value": "ItemIcon14"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -150, "y": 50, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 148}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 149}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a4QEYSxzFBH4byffEaQOTH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 150}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_name"], "value": "ItemIcon15"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -50, "y": 50, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 156}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 155}, "asset": {"__uuid__": "08342b57-92d1-4c58-aeb1-b4cbf0e17852", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 157}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a85DoJw49M1r0Vt0AwF5or", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 158}, {"__id__": 160}, {"__id__": 161}, {"__id__": 162}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_name"], "value": "ItemIcon16"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 50, "y": 50, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 164}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eDZImQHdCzrdX61ambbDe"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 166}, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6SeGf6GBHdLlWrw4e8q3s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aecbDPfrpAaYUgDKp1UrNS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 169}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27PmvwOspHV7SFWJIagOru"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 171}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1334, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83C2Yb99FKfLJBOYsWqWi1"}, {"__type__": "f8ea6v9v5VFOKbxcnAPnd6L", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 173}, "nodeReward": {"__id__": 34}, "btnGet": {"__id__": 28}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0y8RiZDlJEZ/CCqpTBDVN"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 175}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "215HrTFMNJPri/lSGfmN2u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 155}, {"__id__": 147}, {"__id__": 139}, {"__id__": 131}, {"__id__": 123}, {"__id__": 115}, {"__id__": 107}, {"__id__": 99}, {"__id__": 91}, {"__id__": 83}, {"__id__": 75}, {"__id__": 67}, {"__id__": 59}, {"__id__": 51}, {"__id__": 43}, {"__id__": 35}]}]