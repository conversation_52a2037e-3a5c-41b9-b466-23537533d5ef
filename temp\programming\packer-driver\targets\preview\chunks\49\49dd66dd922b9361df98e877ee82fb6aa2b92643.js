System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventActionBase, BulletActionBase, BulletAction_Duration, BulletAction_ElapsedTime, BulletAction_PosX, BulletAction_PosY, BulletAction_Speed, BulletAction_SpeedAngle, BulletAction_Acceleration, BulletAction_AccelerationAngle, BulletAction_Scale, BulletAction_ColorR, BulletAction_ColorG, BulletAction_ColorB, BulletAction_FacingMoveDir, BulletAction_Destructive, BulletAction_DestructiveOnHit, _crd;

  function _reportPossibleCrUseOfEventActionBase(extras) {
    _reporterNs.report("EventActionBase", "./IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "../EventGroup", _context.meta, extras);
  }

  _export({
    BulletActionBase: void 0,
    BulletAction_Duration: void 0,
    BulletAction_ElapsedTime: void 0,
    BulletAction_PosX: void 0,
    BulletAction_PosY: void 0,
    BulletAction_Speed: void 0,
    BulletAction_SpeedAngle: void 0,
    BulletAction_Acceleration: void 0,
    BulletAction_AccelerationAngle: void 0,
    BulletAction_Scale: void 0,
    BulletAction_ColorR: void 0,
    BulletAction_ColorG: void 0,
    BulletAction_ColorB: void 0,
    BulletAction_FacingMoveDir: void 0,
    BulletAction_Destructive: void 0,
    BulletAction_DestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      EventActionBase = _unresolved_2.EventActionBase;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "29e6cZjbe5AO5Hs+Z9KZ2+L", "BulletEventActions", undefined);

      _export("BulletActionBase", BulletActionBase = class BulletActionBase extends (_crd && EventActionBase === void 0 ? (_reportPossibleCrUseOfEventActionBase({
        error: Error()
      }), EventActionBase) : EventActionBase) {// this was intentionally left blank
      });

      _export("BulletAction_Duration", BulletAction_Duration = class BulletAction_Duration extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.duration.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.duration.value = value;
        }

      });

      _export("BulletAction_ElapsedTime", BulletAction_ElapsedTime = class BulletAction_ElapsedTime extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.elapsedTime;
        }

        executeInternal(context, value) {
          context.bullet.elapsedTime = value;
        }

      });

      _export("BulletAction_PosX", BulletAction_PosX = class BulletAction_PosX extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.node.position.x;
        }

        executeInternal(context, value) {
          var position = context.bullet.node.position;
          context.bullet.node.setPosition(value, position.y);
        }

      });

      _export("BulletAction_PosY", BulletAction_PosY = class BulletAction_PosY extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.node.position.y;
        }

        executeInternal(context, value) {
          var position = context.bullet.node.position;
          context.bullet.node.setPosition(position.x, value);
        }

      });

      _export("BulletAction_Speed", BulletAction_Speed = class BulletAction_Speed extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.speed.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.speed.value = value;
        }

      });

      _export("BulletAction_SpeedAngle", BulletAction_SpeedAngle = class BulletAction_SpeedAngle extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.speedAngle.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.speedAngle.value = value;
        }

      });

      _export("BulletAction_Acceleration", BulletAction_Acceleration = class BulletAction_Acceleration extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.acceleration.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.acceleration.value = value;
        }

      });

      _export("BulletAction_AccelerationAngle", BulletAction_AccelerationAngle = class BulletAction_AccelerationAngle extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.accelerationAngle.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.accelerationAngle.value = value;
        }

      });

      _export("BulletAction_Scale", BulletAction_Scale = class BulletAction_Scale extends BulletActionBase {
        resetStartValue(context) {
          this._startValue = context.bullet.prop.scale.value;
        }

        executeInternal(context, value) {
          context.bullet.prop.scale.value = value;
        }

      });

      _export("BulletAction_ColorR", BulletAction_ColorR = class BulletAction_ColorR extends BulletActionBase {
        executeInternal(context, value) {
          var color = context.bullet.prop.color.value;
          color.r = value;
          context.bullet.prop.color.value = color;
        }

      });

      _export("BulletAction_ColorG", BulletAction_ColorG = class BulletAction_ColorG extends BulletActionBase {
        executeInternal(context, value) {
          var color = context.bullet.prop.color.value;
          color.g = value;
          context.bullet.prop.color.value = color;
        }

      });

      _export("BulletAction_ColorB", BulletAction_ColorB = class BulletAction_ColorB extends BulletActionBase {
        executeInternal(context, value) {
          var color = context.bullet.prop.color.value;
          color.b = value;
          context.bullet.prop.color.value = color;
        }

      });

      _export("BulletAction_FacingMoveDir", BulletAction_FacingMoveDir = class BulletAction_FacingMoveDir extends BulletActionBase {
        canLerp() {
          return false;
        }

        executeInternal(context, value) {
          context.bullet.prop.isFacingMoveDir.value = value === 1;
        }

      });

      _export("BulletAction_Destructive", BulletAction_Destructive = class BulletAction_Destructive extends BulletActionBase {
        canLerp() {
          return false;
        }

        executeInternal(context, value) {
          context.bullet.prop.isDestructive.value = value === 1;
        }

      });

      _export("BulletAction_DestructiveOnHit", BulletAction_DestructiveOnHit = class BulletAction_DestructiveOnHit extends BulletActionBase {
        canLerp() {
          return false;
        }

        executeInternal(context, value) {
          context.bullet.prop.isDestructiveOnHit.value = value === 1;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=49dd66dd922b9361df98e877ee82fb6aa2b92643.js.map