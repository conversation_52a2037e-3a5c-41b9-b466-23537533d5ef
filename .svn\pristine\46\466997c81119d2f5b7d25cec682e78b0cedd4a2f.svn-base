import { _decorator, Node } from 'cc';

import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { GameIns } from '../../../game/GameIns';

const { ccclass, property } = _decorator;

@ccclass('GamePauseUI')
export class GamePauseUI extends BaseUI {

    public static getUrl(): string { return "ui/game/GamePauseUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }

    protected onLoad(): void {

    }
    async closeUI() {
        UIMgr.closeUI(GamePauseUI);
    }
    async onShow(): Promise<void> {
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }

    onBtnResumeClicked() {
        GameIns.gameRuleManager.gameResume();
        this.closeUI();
    }

    onBtnExitClicked() {
        this.closeUI();
        GameIns.battleManager.quitBattle();
    }
}


