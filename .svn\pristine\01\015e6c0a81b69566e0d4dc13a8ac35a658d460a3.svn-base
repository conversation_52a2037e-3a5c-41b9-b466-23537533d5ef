import { v2, warn } from "cc";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { GameConst } from "../const/GameConst";
import { EnemyWave } from "../data/EnemyWave";
import { StageData } from "../data/StageData";
import { GameIns } from "../GameIns";
import EnemyPlane from "../ui/plane/enemy/EnemyPlane";
import { Tools } from "../utils/Tools";
import { Wave } from "../wave/Wave";

export default class WaveManager extends SingletonBase<WaveManager> {
    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();// 波次配表数据

    private _bEnemyCreateAble: boolean = false;//是否可以创建敌机
    private _bEnemyNorCreateAble: boolean = false;//是否可以创建普通敌机

    private _isAllWaveCompleted: boolean = false;//是否所有波次都已完成
    private _waves: Wave[] = [];

    //boss
    private _bossCreateDelay: number = 0;
    private _bossCreateTime: number = 0;
    private _bossToAddArr: any[] = [];
    private _bShowBossWarning: boolean = false;

    get enemyCreateAble(): boolean {
        return this._bEnemyCreateAble;
    }

    set enemyCreateAble(value: boolean) {
        this._bEnemyCreateAble = value;
    }

    constructor() {
        super();
        // this.initConfig();
    }

    reset(): void {
        this._isAllWaveCompleted = false;
        
        this._bEnemyCreateAble = false;
        this._bEnemyNorCreateAble = false;
        // this._waveActionArr = [];
        // this._waveNumArr = [];
        // this._waveTimeArr = [];
        this._waves = [];
        this._bShowBossWarning = false;
        this._bossCreateTime = 0;
    }

    setEnemyActions(actions: StageData[]): void {
        // this._enemyActions = actions;
    }

    gameStart(): void {
        this._bEnemyCreateAble = true;
        this._bEnemyNorCreateAble = true;
        // this._waveArr = [];
        // this._waveActionArr = [];
        // this._waveNumArr = [];
        // this._waveTimeArr = [];

        // 这里不能清掉，清掉后存在问题: 时序上是先addWaveByLevel，后gameStart
        // this._waves = [];
    }

    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {
        return this._waveNorDatasMap.get(groupID);
    }
    
    addWaveByLevel(wave: Wave, posX: number, posY: number):void {
        wave.trigger(posX, posY);
        this._waves.push(wave);
        console.log(`ybgg addWaveByLevel wave: ${wave}`)

        // const enemyWave = EnemyWave.fromLevelWave(wave, posX, posY);
        // this._waveArr.push(enemyWave)
        // this._waveNumArr.push(0)
        // this._waveTimeArr.push(0)
        // this._waveActionArr.push(this._curEnemyAction!)
        
        // const group = this._waveNorDatasMap.get(enemyWave.enemyGroupID)
        // if (group == null) {
        //     this._waveNorDatasMap.set(enemyWave.enemyGroupID, [enemyWave]);
        // } else {
        //     group.push(enemyWave);
        // }
    }

    async updateGameLogic(deltaTime: number) {
        // this._updateCurAction(deltaTime);
        this._updateWaves(deltaTime);
        this._updateBoss(deltaTime);
    }

    /**
     * 更新当前敌人行为
     * @param deltaTime 每帧的时间增量
     */
    // private _updateCurAction(deltaTime: number): void {
    //     if (!this._enemyOver) {
    //         if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {
    //             this._enemyOver = true;
    //             warn("enemy over");
    //         } else if (this.enemyCreateAble && !this._curEnemyAction) {
    //             const action = this._enemyActions![this._enemyActionIndex];
    //             switch (action.type) {
    //                 case 0:
    //                     this._enemyCreateTime += deltaTime;
    //                     if (
    //                         this._enemyCreateTime >= action.enemyNorInterval ||
    //                         (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)
    //                     ) {
    //                         this._curEnemyAction = action;
    //                     }
    //                     break;
    //                 default:
    //                     if (action.type >= 100) {
    //                         console.warn("Boss stage", action.type, action.enemyNorIDs[0]);
    //                         this._bossCreateDelay = action.enemyNorInterval;
    //                         this._bossToAddArr.push(action);
    //                         this._enemyActionIndex++;
    //                     }
    //             }
    //         }
    //     }
    // }

    /**
     * 更新敌人逻辑
     * @param deltaTime 每帧的时间增量
     */
    // private async _updateEnemy(deltaTime: number) {
    //     // await this._updateEnemyCreate(deltaTime);

    //     if (this._curEnemyAction) {
    //         if (!this._updateNorEnemys(deltaTime)) {
    //             this._curEnemyAction = null;
    //             this._enemyActionIndex++;
    //             this._enemyCreateTime = 0;
    //         }
    //     }
    // }

    private _updateWaves(deltaTime: number) {
        const dtInMiliseconds = deltaTime * 1000;
        for (let i = this._waves.length - 1; i >= 0; i--) {
            const wave = this._waves[i];
            wave.tick(dtInMiliseconds);
            if (wave.isCompleted) {
                this._waves.splice(i, 1);
            }
        }
    }

    /**
     * 更新敌人生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    // private async _updateEnemyCreate(deltaTime: number): Promise<void> {
    //     for (let i = 0; i < this._waveArr.length; i++) {
    //         const wave = this._waveArr[i];
    //         this._waveTimeArr[i] += deltaTime;
    //         const currentEnemyCount = this._waveNumArr[i];
    //         let posX = GameConst.EnemyPos.x;
    //         let posY = GameConst.EnemyPos.y;

    //         if (wave.bSetStartPos) {
    //             posX += wave.startPosX;
    //             posY += wave.startPosY;
    //         }

    //         for (let j = currentEnemyCount; j < wave.enemyNum; j++) {
    //             if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {
    //                 this._waveNumArr[i]++;
    //                 let enemy:EnemyPlane|null;
    //                 const enemyPosX = posX + wave.posDX * (j + 1);
    //                 const enemyPosY = posY + wave.posDY * (j + 1);

    //                 switch (wave.type) {
    //                     case 0:
    //                         enemy = await GameIns.enemyManager.addPlane(wave.enemyID);
    //                         if (enemy) {
    //                             if (j < wave.firstShootDelay.length) {
    //                                 enemy.setFirstShootDelay(wave.firstShootDelay[j]);
    //                             }
    //                             enemy.setStandByTime(0);
    //                             enemy.initTrack(
    //                                 wave.trackGroups,
    //                                 wave.liveParam,
    //                                 enemyPosX,
    //                                 enemyPosY,
    //                                 wave.rotateSpeed
    //                             );
    //                         }
    //                         break;
    //                 }
    //             }
    //         }

    //         if (wave.enemyNum <= this._waveNumArr[i]) {
    //             this._waveArr.splice(i, 1);
    //             this._waveNumArr.splice(i, 1);
    //             this._waveTimeArr.splice(i, 1);
    //             this._waveActionArr.splice(i, 1);
    //             i--;
    //         }
    //     }
    // }

    /**
     * 更新普通敌人生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    // private _updateNorEnemys(deltaTime: number): boolean {
    //     if (this._bEnemyNorCreateAble) {
    //         if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {
    //             this._waveIndex = 0;
    //             return false;
    //         }

    //         const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];
    //         this._waveCreateTime += deltaTime;

    //         const waveDatas = this.getNorWaveDatas(waveID);
    //         if (!waveDatas) {
    //             return false;
    //         }
    //         console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);
    //         for (let i = 0; i < waveDatas!.length; i++) {
    //             const wave = waveDatas![i];
    //             if (
    //                 !Tools.arrContain(this._waveIndexOver, i) &&
    //                 this._waveCreateTime >= wave.groupInterval
    //             ) {
    //                 this._waveArr.push(wave);
    //                 this._waveNumArr.push(0);
    //                 this._waveTimeArr.push(0);
    //                 this._waveActionArr.push(this._curEnemyAction);
    //                 this._waveIndexOver.push(i);
    //             }
    //         }

    //         if (this._waveIndexOver.length >= waveDatas!.length) {
    //             this._waveIndexOver.splice(0);
    //             this._waveCreateTime = 0;
    //             this._waveIndex++;
    //         }
    //     }

    //     return true;
    // }

    /**
     * 更新 Boss 生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    private _updateBoss(deltaTime: number): void {
        if (this._bossToAddArr.length > 0 && GameIns.enemyManager.isEnemyOver()){
            this._bossCreateTime += deltaTime;
            if (this._bossCreateTime > this._bossCreateDelay) {
                const bossData = this._bossToAddArr[0];
                GameIns.bossManager.addBoss(bossData.enemyNorIDs[0]);
                this._bossToAddArr.splice(0, 1);
                GameIns.battleManager.bossWillEnter();
            }
        }
    }
    /**
     * 检查敌人是否全部结束
     * @returns 是否所有敌人都已结束
     */
    isEnemyOver(): boolean {
        //return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;
        return this._isAllWaveCompleted && this._waves.length === 0 && this._bossToAddArr.length === 0;
    }
}