{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsScoreCell.ts"], "names": ["_decorator", "Component", "ccclass", "property", "StatisticsScoreCell", "start", "update", "deltaTime"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;qCAGjBI,mB,WADZF,OAAO,CAAC,qBAAD,C,gBAAR,MACaE,mBADb,SACyCH,SADzC,CACmD;AAC/CI,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAP8C,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('StatisticsScoreCell')\r\nexport class StatisticsScoreCell extends Component {\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n}\r\n\r\n\r\n"]}