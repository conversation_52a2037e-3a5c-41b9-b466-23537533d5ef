import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';
import { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass('WaveTrack')
export class WaveTrack {
    @property(CCInteger)
    public id = 0;
    @property(CCFloat)
    public speed = 0;
    @property(CCFloat)
    public accelerate = 0;
    @property(CCFloat)
    public Interval = 0;
}

@ccclass('WaveTrackGroup')
export class WaveTrackGroup {
    @property(CCInteger)
    public type = 0;
    @property(CCInteger)
    public loopNum = 0;
    @property(CCInteger)
    public formIndex = 0;
    @property([WaveTrack])
    public tracks: WaveTrack[] = [];
}

@ccclass('Wave')
@menu("怪物/波次")
@executeInEditMode()
export class Wave extends Component {
    @property({type:WaveData})
    readonly waveData: WaveData = new WaveData();

    /*
     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
     */
    private _isCompleted: boolean = false;
    // 当前波次是否已完成
    public get isCompleted() { return this._isCompleted; }
    private _waveElapsedTime: number = 0;
    private _nextSpawnTime: number = 0;
    private _totalWeight: number = 0;
    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion
    private _waveCompleteParam: number = 0;
    // 以下两个是用在waveCompletion == SpawnCount时的队列
    private _nextSpawnIndex: number = 0;
    private _spawnQueue: number[] = [];
    // 当前wave的偏移位置
    private _offsetX: number = 0;
    private _offsetY: number = 0;

    private _reset() {
        this._isCompleted = false;
        this._waveElapsedTime = 0;
        this._nextSpawnTime = 0;
        this._nextSpawnIndex = 0;
        this._spawnQueue.length = 0;
        // this._spawnQueue = this.waveData.planeList;
    }

    onLoad() {
        if (this.waveData && this.waveData.spawnOrder === eSpawnOrder.Random) {
            this._totalWeight = 0;
            // add up _totalWeight if is random
            this.waveData.spawnGroup.forEach((group) => {
                this._totalWeight += group.weight;
                group.selfWeight = this._totalWeight;
            });
        }
    }

    trigger(x: number, y: number) {
        this._reset();
        this._offsetX = x;
        this._offsetY = y;

        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可
        if (this.waveData) {
            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();
            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
                if (this.waveData.spawnOrder === eSpawnOrder.Random) {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        const randomWeight = Math.random() * this._totalWeight;
                        for (const group of this.waveData.spawnGroup) {
                            if (randomWeight <= group.selfWeight) {
                                this._spawnQueue.push(group.planeID);
                                break;
                            }
                        }
                    }
                } else {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        // 通过取余实现循环
                        this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);
                    }
                }
            }
        }
    }

    // tick wave
    tick(dtInMiliseconds: number) {
        if (this._isCompleted) return;

        this._waveElapsedTime += dtInMiliseconds;
        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
            // 产出固定数量的波次
            if (this._waveElapsedTime >= this._nextSpawnTime) {
                if (!this.spawnFromQueue()) {
                    this._isCompleted = true;
                }
            }
        }
        else {
            // 完全根据时间的波次
            if (this._waveElapsedTime >= this._waveCompleteParam) {
                this._isCompleted = true;
            } else {
                if (this._waveElapsedTime >= this._nextSpawnTime) {
                    this.spawnFromGroup();
                }
            }
        }
    }

    private spawnFromQueue(): boolean {        
        if (this._nextSpawnIndex >= this._spawnQueue.length) {
            return false;
        }

        this.spawnSingleFromQueue(this._nextSpawnIndex++);
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
        return true;
    }

    private spawnSingleFromQueue(index: number): void {
        if (index >= this._spawnQueue.length) {
            return;
        }

        let spawnPos = this.waveData.spawnPos;
        let spawnAngle = this.waveData.spawnAngle.eval();
        // let spawnSpeed = this.waveData.spawnSpeed.eval();

        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);
    }

    private spawnFromGroup(): void {
        let spawnPos = this.waveData.spawnPos;
        let spawnAngle = this.waveData.spawnAngle.eval();
        // let spawnSpeed = this.waveData.spawnSpeed.eval();

        if (this.waveData.spawnOrder === eSpawnOrder.Random) {
            const randomWeight = Math.random() * this._totalWeight;
            for (const group of this.waveData.spawnGroup) {
                if (randomWeight <= group.selfWeight) {
                    this.createPlane(group.planeID, spawnPos, spawnAngle);
                    break;
                }
            }
        } else {
            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);
        }
    }

    private async createPlane(planeId: number, pos: Vec2, angle: number) {
        let enemy = GameIns.enemyManager.addPlane(planeId, null);
        if (enemy) {
            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);
            // enemy.setStandByTime(0);
            // console.log("createPlane", planeId, pos, angle, speed);
            enemy.setPos(this._offsetX + pos.x, this._offsetY + pos.y);
            // enemy.setPos(pos.x, pos.y);
            // enemy.initMove(speed, angle, this.waveData.delayDestroy);
        }
    }

}