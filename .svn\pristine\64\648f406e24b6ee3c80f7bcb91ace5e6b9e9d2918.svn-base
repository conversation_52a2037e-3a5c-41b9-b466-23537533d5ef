import { _decorator, Node, RichText, UITransform, WebView } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass('AnnouncementUI')
export class AnnouncementUI extends BaseUI {
    @property(Node)
    content: Node | null = null;

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;

    @property(RichText)
    richText: RichText | null = null;

    public static getUrl(): string { return "prefab/ui/AnnouncementUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
    }
    async onOKClick() {
        UIMgr.closeUI(AnnouncementUI);
    }

    async onShow(messgae: string): Promise<void> {
        let wid = this.content!.getComponent(UITransform)!.width;
        let hei = this.richText!.getComponent(UITransform)!.height;
        hei = hei < 800 ? 800 : hei;
        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {

    }
}
