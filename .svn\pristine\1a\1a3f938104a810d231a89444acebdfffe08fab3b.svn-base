import { _decorator, Component, Enum, Rect, rect, v2, Vec2 } from 'cc';
import { GameIns } from '../GameIns';
import Entity from '../ui/base/Entity';
const { ccclass, property, menu } = _decorator;

export enum ColliderType {
    Circle = 1,
    Box = 2,
    Polygon = 4,//凸多边形
}

//碰撞分组
export let ColliderGroupType = Enum({
    DEFAULT: 1,
    PLAYER: 2,
    BULLET_SELF: 3,
    ENEMY_NORMAL: 4,
    BULLET_ENEMY: 5,
    ENEMY_BUILE: 6,
    PROP: 7,//道具
})


@ccclass
export default class FCollider extends Component {
    public get type() {
        return ColliderType.Box;
    }
    public isConvex: boolean = true;
    public isEnable: boolean = true;//是否启用碰撞检测
    public isImmunityBullet: boolean = false;//是否免疫子弹碰撞（不碰撞，直接穿透）
    public isImmunityBulletHurt: boolean = false;//是否免疫子弹碰撞伤害（会碰撞，没伤害）
    public isImmunityCollider: boolean = false;//无视撞击(免疫撞击也免疫子弹)
    public isImmunityColliderHurt: boolean = false;//是否免疫碰撞伤害(免疫撞击也免疫子弹)
    public entity: Entity | null = null;

    //自增id
    private static _baseId: number = 1;

    //AABB
    public aabb: Rect = rect();

    public get x() { return this.aabb.x; }
    public get y() { return this.aabb.y; }
    public get width() { return this.aabb.width; }
    public get height() { return this.aabb.height; }

    public colliderId: number = 0;
    public initCollider() {
        this.colliderId = FCollider._baseId++;
        if (FCollider._baseId > 5e10) {//防止id太大，做个轮回
            FCollider._baseId = 1;
        }
    }

    @property(Vec2)
    private _offset: Vec2 = v2();

    @property(Vec2)
    public get offset(): Vec2 {
        return this._offset;
    }
    public set offset(value: Vec2) {
        this._offset = value;
    }

    @property({
        type: ColliderGroupType,
        displayName: '碰撞分组',
    })
    groupType: number = ColliderGroupType.DEFAULT;

    protected onEnable(): void {
        GameIns.fColliderManager.addCollider(this);
    }

    protected start(): void {
        this.draw();
    }
    
    draw(){

    }

    protected onDisable(): void {
        GameIns.fColliderManager.removeCollider(this);
    }

    initBaseData(entity:Entity, offset: Vec2 = v2(0, 0)) {
        this.entity = entity;
        this.offset = offset;
    }

}
