[{"__type__": "cc.SceneAsset", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 133}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 134}, "_id": "401efd7e-bd20-4537-a13a-f25e6238c2a9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 30}, {"__id__": 77}, {"__id__": 91}, {"__id__": 105}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 123}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 667, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 29}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46ihxbAfVPT4CTuCqzBBFA"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}], "_active": true, "_components": [{"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1370.6399999999885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d47gUK/vNBU7+C5ZHLdsDC"}, {"__type__": "cc.Node", "_name": "backgrounds", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "496wUXvK1P8pBsfn+yPx9L"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "89/KzdMdVGMbgzmKm6DD7s", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 11}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -27, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "8f8d474d-37f5-4010-9299-22846c394020", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 15}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4caICgxhBJ9a7wWrszoBdx", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 1253, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 19}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 20}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a164zj9itMaZNBdcoF4Qnz", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 21}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "86KO+WpZZGMY98gBFgucUw"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62LQ4Bh/tDUJ1ImNr96mPO"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d3QYHSWqJHW7uh2H1/xOPw"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c3r1SweKVCMr1V/+DXYxT2"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "feilNRIVFL1IeqTZWkSMI4"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_id": "81mmHYOYJESbY/w3mht5yn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c7cr3UHz9ID5OL86RVgF3A"}, {"__type__": "cc.Node", "_name": "FloorLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 31}, {"__id__": 48}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62oMpiqNVJeLfnCzN4mnAI"}, {"__type__": "cc.Node", "_name": "layer_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 30}, "_children": [{"__id__": 32}, {"__id__": 42}, {"__id__": 43}, {"__id__": 45}, {"__id__": 46}], "_active": true, "_components": [{"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1370.6399999999885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6gLF9mVFFxYiIFrd8OMcv"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}, {"__id__": 36}, {"__id__": 39}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "29Eox1ISBPKodefMo62NrQ"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 34}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "88579382-4a8f-4fc0-b548-49967343c11c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 35}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "98mg1Oi0BKoqb7Uu0k0GyB", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 37}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 36}, "asset": {"__uuid__": "6c602340-6d8c-4240-a6bf-c53d4dcf1ebf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 38}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "baiFUQNW9DRKl8492uJ+QG", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 40}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "6d35985a-2571-4129-a1e6-bd3334135fa1", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 41}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "df/2Ep36dLJ6/bEJTPKjY+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2beSLpvAZK6KHT/xS3lnMP"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [{"__id__": 44}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ae4TV0Mv1EDLZFzhOdbWG7"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 43}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "01kfM4d8BEpL+vofBgLVcK"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a1TJjgYqJIUYpKtdm5bLZB"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8eRDkT4K9Lp66nLsH84APJ"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_id": "b2lcjDAh1KMZa0xANIWBb8"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 30}, "_children": [{"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 67}, {"__id__": 68}], "_active": true, "_components": [{"__id__": 76}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1370.6399999999885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "735fUeTBxOR4SNtW76rl28"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "09a7Z1wRZDzrV1G77MofE/"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "51RG4mLuJJ660ALRm58xGe"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [{"__id__": 52}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fiw6CiUNExIuXM4hI7/C9"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 51}, "_children": [{"__id__": 53}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "119Uenw0pFVaK1njFihFbt"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 52}, "_prefab": {"__id__": 54}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 53}, "asset": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 55}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a0pNTxgo5Gzrf0hta8ibV/", "prefabRootNode": null, "mountedChildren": [{"__id__": 56}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 57}, "nodes": [{"__id__": 58}, {"__id__": 61}, {"__id__": 64}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 59}, "__editorExtras__": {"mountedRoot": {"__id__": 53}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "e549918d-a699-468c-9d07-2b1c9dbc1a7a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 60}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f8U8Jy89JCuZRJMOxAqOtv", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 62}, "__editorExtras__": {"mountedRoot": {"__id__": 53}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 61}, "asset": {"__uuid__": "db54204b-56fb-485e-bc0c-66149c7951c3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 63}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a9cjxJwWlOSIwoE/5SUIUm", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 65}, "__editorExtras__": {"mountedRoot": {"__id__": 53}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "e3ae5be9-b8ca-450f-a8f7-edd149148782", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 66}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "57K7NUU15FWZslDNs+H1V3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0c+NP8DiJPG6rIML74/VIK"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [{"__id__": 69}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4av8q7xA1FZLgqWcCUp30x"}, {"__type__": "cc.Node", "_name": "wave", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 119.213, "y": 508.433, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c3/jumFrpGPbXvUOQ2n45O"}, {"__type__": "9fa8bQG+gZMmpqlZsjy9rl1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "conditions": [{"__id__": 71}, {"__id__": 72}], "triggers": [{"__id__": 73}, {"__id__": 74}, {"__id__": 75}], "_id": "39U5PTxTFEmKnuG+uy5ysB"}, {"__type__": "LevelEditorCondition"}, {"__type__": "LevelEditorCondition"}, {"__type__": "LevelEditorEventTrigger"}, {"__type__": "LevelEditorEventTrigger"}, {"__type__": "LevelEditorEventTrigger"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_id": "327gVdAPND3JNFxT5hZfO0"}, {"__type__": "cc.Node", "_name": "MainPlane", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 78}, {"__id__": 80}, {"__id__": 85}], "_active": true, "_components": [{"__id__": 90}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5Llji/3pG8ZUsk+/AnCts"}, {"__type__": "cc.Node", "_name": "enemy", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 79}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22ge7J4Q5BcrDOrtqtJWcy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0e4ol/pP9AdL9y81NxK1IW"}, {"__type__": "cc.Node", "_name": "Plane 128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [{"__id__": 81}], "_active": true, "_components": [{"__id__": 84}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbYdiDAGtJNILT2mAHeQHg"}, {"__type__": "cc.Node", "_name": "128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 80}, "_children": [], "_active": true, "_components": [{"__id__": 82}, {"__id__": 83}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -146.727, "y": -381.491, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1innlyJpPfbCEAHtzu7nR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 126, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcvUM8lEVG7r+Duly9xTUi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bcbad599-6805-464f-b852-c6330a6cc136@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b9z9YmlO1ORJddzAvkDa26"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "69CWNsBiBF8qLjUsjYBXVD"}, {"__type__": "cc.Node", "_name": "Plane 150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [{"__id__": 86}], "_active": true, "_components": [{"__id__": 89}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fSyYswbxJMYFuKxLm+oIa"}, {"__type__": "cc.Node", "_name": "150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 49.887, "y": -384.426, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97bvio7axI36wm/98rDd6y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4c8v2GIyBL7p+TWWFkd8E0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "13a76ed5-7bc0-444c-b47f-8beab0558280@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c3kMW+vo1DD4E5mvwzvbme"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dfuqEV4hA5JIX88WPdF7X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dVZOPnxBB55oxOGHxThGs"}, {"__type__": "cc.Node", "_name": "SkyLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 92}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3kr0ELyxNI6WeWVx+sGNb"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 91}, "_children": [{"__id__": 93}, {"__id__": 100}, {"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_active": true, "_components": [{"__id__": 104}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -799.5399999999934, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48hAoDGlhNhandGIOx6d2b"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [{"__id__": 94}, {"__id__": 97}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cduLbhFu5POqW2syVZRMM7"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 93}, "_prefab": {"__id__": 95}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 94}, "asset": {"__uuid__": "b37ff484-6217-472b-bf28-09319f8a31bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 96}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "19J1SgQxdBMYLtooUzJ4rL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 93}, "_prefab": {"__id__": 98}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 97}, "asset": {"__uuid__": "5b101290-4eae-4e19-b77b-6674f856e767", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 99}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "86hmU+mlJEH65OlHW1CoxA", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7az9RJub9Hj6G/rd6t9tyZ"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "86vmF8G2xA95Yfi5Ybb0uF"}, {"__type__": "cc.Node", "_name": "waves", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96UoEnqdNIEoQW8UpCOuSk"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "87O2CmuixBOJj7z9THmn8+"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_id": "82OrIk8dBIOZiUtjrpxnKV"}, {"__type__": "cc.Node", "_name": "DrawNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 106}, {"__id__": 109}, {"__id__": 112}, {"__id__": 115}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33t5ymWlpLkbT5idbqGQ7X"}, {"__type__": "cc.Node", "_name": "draw<PERSON>iew", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65LbppaIRGz7EVQ8Ktul+X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1Cf74eydN0Yy5qbbxInaR"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "75zN+z8lVGqLYHx2jxa0qd"}, {"__type__": "cc.Node", "_name": "drawMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9btiDQ+VZN95aFsLmdMyGC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9eg7PH/u9Il7l5kqhTVfqD"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "f7nWk9GTBEfo/KF0FlisGD"}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": false, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 621.287, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03lbeQRQBBaJmCmQbmnIP1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "0aPFKUoHhJCY99iaVm3K/C"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "时间：11.42", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d7uTBlts9OhJ188cabfoNE"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": false, "_components": [{"__id__": 116}, {"__id__": 117}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 454.781, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cW5DvaP9E7bNc04cxSN9/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "d5aSmlRnRDlJwqRXq14CD4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "进度：0.38", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "038VoLC5NCxYX7kf/kDiDH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cewjtt/5IRbxIivL8bKq5"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "23xs7GFb1G6bRSeyAo5w9K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "68a25Vb5mhGApMaV59XFjQ0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "progress": 0.38073333333333015, "_id": "61Fg6c5BVE0I4TKZPyCZ+2"}, {"__type__": "a4bf2J2KGJJV7RbX1jwoQWJ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "levelname": "adb", "totalTime": 30, "backgroundLayer": {"__id__": 124}, "floorLayers": [{"__id__": 125}, {"__id__": 129}], "skyLayers": [{"__id__": 132}], "_id": "f2rN/MimJBS6HE7SikeuYU"}, {"__type__": "LevelEditorBackgroundLayer", "remark": "", "zIndex": 0, "node": {"__id__": 6}, "speed": 120, "scrollLayers": [], "randomLayers": [], "backgrounds": [{"__uuid__": "0d34869a-29a8-4f06-bc75-2e96c1da964a", "__expectedType__": "cc.Prefab"}, {"__uuid__": "8f8d474d-37f5-4010-9299-22846c394020", "__expectedType__": "cc.Prefab"}]}, {"__type__": "LevelEditorLayer", "remark": "", "zIndex": 0, "node": {"__id__": 48}, "speed": 120, "scrollLayers": [], "randomLayers": [{"__id__": 126}]}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 127}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": [{"__id__": 128}]}, {"__type__": "LevelEditorRandTerrainUI", "weight": 100, "terrainElement": {"__uuid__": "8ab54a00-9127-44b9-8bc1-51d318148fab", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorLayer", "remark": "", "zIndex": 0, "node": {"__id__": 31}, "speed": 120, "scrollLayers": [], "randomLayers": [{"__id__": 130}]}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 131}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": []}, {"__type__": "LevelEditorLayer", "remark": "", "zIndex": 0, "node": {"__id__": 92}, "speed": 70, "scrollLayers": [], "randomLayers": []}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "401efd7e-bd20-4537-a13a-f25e6238c2a9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 33}, {"__id__": 36}, {"__id__": 39}, {"__id__": 53}, {"__id__": 58}, {"__id__": 61}, {"__id__": 64}, {"__id__": 94}, {"__id__": 97}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 135}, "shadows": {"__id__": 136}, "_skybox": {"__id__": 137}, "fog": {"__id__": 138}, "octree": {"__id__": 139}, "skin": {"__id__": 140}, "lightProbeInfo": {"__id__": 141}, "postSettings": {"__id__": 142}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]