import { _decorator, Component, Label, Node, ProgressBar, Sprite } from 'cc';
import csproto, { comm } from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { logError } from 'db://assets/scripts/utils/Logger';
import { MyApp } from '../../../app/MyApp';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
const { ccclass, property } = _decorator;

@ccclass('TaskItem')
export class TaskItem extends Component {
    @property(Label)
    taskDesc: Label | null = null;
    @property(ProgressBar)
    progressBar: ProgressBar | null = null;
    @property(Label)
    progressLabel: Label | null = null;
    @property(Sprite)
    taskIcon: Sprite | null = null;
    @property(ButtonPlus)
    taskJumpBtn: ButtonPlus | null = null;
    @property(ButtonPlus)
    taskGetRewardBtn: ButtonPlus | null = null;
    @property(Node)
    taskDoneNode: Node | null = null;

    protected onLoad(): void {
        this.taskJumpBtn!.addClick(this.onClickTaskJumpBtn, this);
        this.taskGetRewardBtn!.addClick(this.onClickTaskGetRewardBtn, this);
    }

    onRender(taskInfo: csproto.cs.ICSTaskInfo): void {
        const taskCfg = MyApp.lubanTables.TbResTask.get(taskInfo.task_id!);
        if (!taskCfg) {
            logError("TaskItem", `task id ${taskInfo.task_id} not found`)
            return
        }
        this.taskDesc!.string = taskCfg.taskGoal.desc;
        this.progressLabel!.string = `${taskInfo.progress}/${taskCfg.taskGoal.params[1]}`;
        this.progressBar!.progress = taskInfo.progress! / taskCfg.taskGoal.params[1]
        switch (taskInfo.status) {
            case comm.TASK_STATUS.TASK_STATUS_COMPLETE:
                this.taskGetRewardBtn!.node.active = true;
                this.taskJumpBtn!.node.active = false
                this.taskDoneNode!.active = false;
                break;
            case comm.TASK_STATUS.TASK_STATUS_NORMAL:
                this.taskGetRewardBtn!.node.active = true;
                this.taskJumpBtn!.node.active = false
                this.taskDoneNode!.active = false;
                break;
            case comm.TASK_STATUS.TASK_STATUS_AWARD_DONE:
                this.taskGetRewardBtn!.node.active = false;
                this.taskJumpBtn!.node.active = false
                this.taskDoneNode!.active = true;
                break;
            default:
                logError("TaskItem", `task id ${taskInfo.task_id} status error ${taskInfo.status}`)
                break;
        }
        // this.taskIcon!.spriteFrame = MyApp.resMgr.getSpriteFrame(taskCfg.taskIcon);
    }

    private onClickTaskJumpBtn(): void {

    }

    private onClickTaskGetRewardBtn(): void {

    }
}