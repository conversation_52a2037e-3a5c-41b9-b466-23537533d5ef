2025-9-17 10:39:05-debug: start **** info
2025-9-17 10:39:05-log: Cannot access game frame or container.
2025-9-17 10:39:06-debug: asset-db:require-engine-code (417ms)
2025-9-17 10:39:06-log: meshopt wasm decoder initialized
2025-9-17 10:39:06-log: [bullet]:bullet wasm lib loaded.
2025-9-17 10:39:06-log: [box2d]:box2d wasm lib loaded.
2025-9-17 10:39:06-log: Forward render pipeline initialized.
2025-9-17 10:39:06-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.87MB, end 80.06MB, increase: 49.19MB
2025-9-17 10:39:06-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.96MB, end 84.06MB, increase: 3.10MB
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.09MB, end 290.53MB, increase: 206.44MB
2025-9-17 10:39:06-log: Cocos Creator v3.8.6
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 288.92MB, increase: 208.84MB
2025-9-17 10:39:06-log: Using legacy pipeline
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.82MB, end 288.89MB, increase: 208.08MB
2025-9-17 10:39:07-debug: run package(google-play) handler(enable) start
2025-9-17 10:39:07-debug: run package(google-play) handler(enable) success!
2025-9-17 10:39:07-debug: run package(harmonyos-next) handler(enable) start
2025-9-17 10:39:07-debug: run package(harmonyos-next) handler(enable) success!
2025-9-17 10:39:07-debug: run package(honor-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-agc) handler(enable) start
2025-9-17 10:39:07-debug: run package(honor-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(huawei-agc) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-quick-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(linux) handler(enable) start
2025-9-17 10:39:07-debug: run package(ios) handler(enable) success!
2025-9-17 10:39:07-debug: run package(linux) handler(enable) success!
2025-9-17 10:39:07-debug: run package(mac) handler(enable) success!
2025-9-17 10:39:07-debug: run package(migu-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(mac) handler(enable) start
2025-9-17 10:39:07-debug: run package(migu-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(native) handler(enable) success!
2025-9-17 10:39:07-debug: run package(native) handler(enable) start
2025-9-17 10:39:07-debug: run package(ios) handler(enable) start
2025-9-17 10:39:07-debug: run package(oppo-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(ohos) handler(enable) success!
2025-9-17 10:39:07-debug: run package(ohos) handler(enable) start
2025-9-17 10:39:07-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-17 10:39:07-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-17 10:39:07-debug: run package(taobao-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(web-desktop) handler(enable) start
2025-9-17 10:39:07-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(web-desktop) handler(enable) success!
2025-9-17 10:39:07-debug: run package(vivo-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(web-mobile) handler(enable) start
2025-9-17 10:39:07-debug: run package(web-mobile) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatgame) handler(enable) start
2025-9-17 10:39:07-debug: run package(wechatgame) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatprogram) handler(enable) start
2025-9-17 10:39:07-debug: run package(windows) handler(enable) start
2025-9-17 10:39:07-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatprogram) handler(enable) success!
2025-9-17 10:39:07-debug: run package(cocos-service) handler(enable) start
2025-9-17 10:39:07-debug: run package(cocos-service) handler(enable) success!
2025-9-17 10:39:07-debug: run package(im-plugin) handler(enable) success!
2025-9-17 10:39:07-debug: run package(windows) handler(enable) success!
2025-9-17 10:39:07-debug: run package(im-plugin) handler(enable) start
2025-9-17 10:39:07-debug: run package(emitter-editor) handler(enable) start
2025-9-17 10:39:07-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-17 10:39:07-debug: run package(emitter-editor) handler(enable) success!
2025-9-17 10:39:07-debug: asset-db:worker-init: initPlugin (1042ms)
2025-9-17 10:39:07-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db:worker-init start:30.86MB, end 291.14MB, increase: 260.28MB
2025-9-17 10:39:07-debug: Run asset db hook programming:beforePreStart ...
2025-9-17 10:39:07-debug: Run asset db hook programming:beforePreStart success!
2025-9-17 10:39:07-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-17 10:39:07-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-17 10:39:07-debug: run package(localization-editor) handler(enable) start
2025-9-17 10:39:07-debug: asset-db-hook-programming-beforePreStart (108ms)
2025-9-17 10:39:07-debug: asset-db:worker-init (1633ms)
2025-9-17 10:39:07-debug: run package(localization-editor) handler(enable) success!
2025-9-17 10:39:07-debug: asset-db-hook-engine-extends-beforePreStart (108ms)
2025-9-17 10:39:07-debug: Preimport db internal success
2025-9-17 10:39:07-debug: run package(wave-editor) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wave-editor) handler(enable) start
2025-9-17 10:39:07-debug: run package(placeholder) handler(enable) start
2025-9-17 10:39:07-debug: run package(placeholder) handler(enable) success!
2025-9-17 10:39:07-debug: Run asset db hook programming:afterPreStart ...
2025-9-17 10:39:07-debug: Preimport db assets success
2025-9-17 10:39:07-debug: starting packer-driver...
2025-9-17 10:39:12-debug: initialize scripting environment...
2025-9-17 10:39:12-debug: [[Executor]] prepare before lock
2025-9-17 10:39:12-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-17 10:39:12-debug: [[Executor]] prepare after unlock
2025-9-17 10:39:12-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-17 10:39:12-debug: Run asset db hook programming:afterPreStart success!
2025-9-17 10:39:12-debug: [Assets Memory track]: asset-db:worker-init: preStart start:291.15MB, end 305.06MB, increase: 13.91MB
2025-9-17 10:39:12-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-17 10:39:12-debug: Start up the 'internal' database...
2025-9-17 10:39:12-debug: asset-db-hook-programming-afterPreStart (5431ms)
2025-9-17 10:39:12-debug: asset-db:worker-effect-data-processing (224ms)
2025-9-17 10:39:12-debug: asset-db-hook-engine-extends-afterPreStart (224ms)
2025-9-17 10:39:12-debug: Start up the 'assets' database...
2025-9-17 10:39:12-debug: asset-db:worker-startup-database[internal] (5731ms)
2025-9-17 10:39:13-debug: lazy register asset handler *
2025-9-17 10:39:13-debug: lazy register asset handler directory
2025-9-17 10:39:13-debug: lazy register asset handler json
2025-9-17 10:39:13-debug: lazy register asset handler spine-data
2025-9-17 10:39:13-debug: lazy register asset handler dragonbones
2025-9-17 10:39:13-debug: lazy register asset handler text
2025-9-17 10:39:13-debug: lazy register asset handler dragonbones-atlas
2025-9-17 10:39:13-debug: lazy register asset handler terrain
2025-9-17 10:39:13-debug: lazy register asset handler javascript
2025-9-17 10:39:13-debug: lazy register asset handler prefab
2025-9-17 10:39:13-debug: lazy register asset handler typescript
2025-9-17 10:39:13-debug: lazy register asset handler sprite-frame
2025-9-17 10:39:13-debug: lazy register asset handler tiled-map
2025-9-17 10:39:13-debug: lazy register asset handler buffer
2025-9-17 10:39:13-debug: lazy register asset handler scene
2025-9-17 10:39:13-debug: lazy register asset handler sign-image
2025-9-17 10:39:13-debug: lazy register asset handler image
2025-9-17 10:39:13-debug: lazy register asset handler texture
2025-9-17 10:39:13-debug: lazy register asset handler alpha-image
2025-9-17 10:39:13-debug: lazy register asset handler erp-texture-cube
2025-9-17 10:39:13-debug: lazy register asset handler texture-cube-face
2025-9-17 10:39:13-debug: lazy register asset handler texture-cube
2025-9-17 10:39:13-debug: lazy register asset handler render-texture
2025-9-17 10:39:13-debug: lazy register asset handler gltf
2025-9-17 10:39:13-debug: lazy register asset handler rt-sprite-frame
2025-9-17 10:39:13-debug: lazy register asset handler gltf-material
2025-9-17 10:39:13-debug: lazy register asset handler gltf-animation
2025-9-17 10:39:13-debug: lazy register asset handler gltf-scene
2025-9-17 10:39:13-debug: lazy register asset handler gltf-skeleton
2025-9-17 10:39:13-debug: lazy register asset handler gltf-embeded-image
2025-9-17 10:39:13-debug: lazy register asset handler fbx
2025-9-17 10:39:13-debug: lazy register asset handler gltf-mesh
2025-9-17 10:39:13-debug: lazy register asset handler physics-material
2025-9-17 10:39:13-debug: lazy register asset handler material
2025-9-17 10:39:13-debug: lazy register asset handler audio-clip
2025-9-17 10:39:13-debug: lazy register asset handler effect
2025-9-17 10:39:13-debug: lazy register asset handler animation-graph
2025-9-17 10:39:13-debug: lazy register asset handler effect-header
2025-9-17 10:39:13-debug: lazy register asset handler animation-clip
2025-9-17 10:39:13-debug: lazy register asset handler animation-graph-variant
2025-9-17 10:39:13-debug: lazy register asset handler animation-mask
2025-9-17 10:39:13-debug: lazy register asset handler bitmap-font
2025-9-17 10:39:13-debug: lazy register asset handler ttf-font
2025-9-17 10:39:13-debug: lazy register asset handler particle
2025-9-17 10:39:13-debug: lazy register asset handler render-pipeline
2025-9-17 10:39:13-debug: lazy register asset handler auto-atlas
2025-9-17 10:39:13-debug: lazy register asset handler label-atlas
2025-9-17 10:39:13-debug: lazy register asset handler sprite-atlas
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-material
2025-9-17 10:39:13-debug: lazy register asset handler render-flow
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-mesh
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-skeleton
2025-9-17 10:39:13-debug: lazy register asset handler video-clip
2025-9-17 10:39:13-debug: lazy register asset handler render-stage
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-animation
2025-9-17 10:39:13-debug: asset-db:worker-startup-database[assets] (5678ms)
2025-9-17 10:39:13-debug: asset-db:ready (8925ms)
2025-9-17 10:39:13-debug: init worker message success
2025-9-17 10:39:13-debug: fix the bug of updateDefaultUserData
2025-9-17 10:39:13-debug: asset-db:start-database (5799ms)
2025-9-17 10:39:13-debug: programming:execute-script (3ms)
2025-9-17 10:39:13-debug: [Build Memory track]: builder:worker-init start:197.75MB, end 210.15MB, increase: 12.40MB
2025-9-17 10:39:13-debug: builder:worker-init (306ms)
2025-9-17 10:39:53-debug: refresh db internal success
2025-9-17 10:39:53-debug: refresh db assets success
2025-9-17 10:39:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:39:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:39:53-debug: asset-db:refresh-all-database (149ms)
2025-9-17 10:39:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:39:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:40:33-debug: refresh db internal success
2025-9-17 10:40:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:40:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:40:33-debug: refresh db assets success
2025-9-17 10:40:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:40:33-debug: asset-db:refresh-all-database (149ms)
2025-9-17 10:40:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:40:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:40:33-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (13ms)
2025-9-17 10:41:18-debug: refresh db internal success
2025-9-17 10:41:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:41:18-debug: refresh db assets success
2025-9-17 10:41:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:41:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:41:18-debug: asset-db:refresh-all-database (146ms)
2025-9-17 10:41:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:41:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:41:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (10ms)
2025-9-17 10:42:26-debug: refresh db internal success
2025-9-17 10:42:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:42:26-debug: refresh db assets success
2025-9-17 10:42:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:42:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:42:26-debug: asset-db:refresh-all-database (148ms)
2025-9-17 10:42:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:42:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:42:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:42:26-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:43:59-debug: refresh db internal success
2025-9-17 10:44:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:44:00-debug: refresh db assets success
2025-9-17 10:44:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:44:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:44:00-debug: asset-db:refresh-all-database (144ms)
2025-9-17 10:44:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:44:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:44:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:44:00-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 10:48:26-debug: refresh db internal success
2025-9-17 10:48:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:48:26-debug: refresh db assets success
2025-9-17 10:48:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:48:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:48:26-debug: asset-db:refresh-all-database (142ms)
2025-9-17 10:48:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:48:26-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:49:06-debug: refresh db internal success
2025-9-17 10:49:06-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:06-debug: refresh db assets success
2025-9-17 10:49:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:49:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:49:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:49:06-debug: asset-db:refresh-all-database (139ms)
2025-9-17 10:49:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:49:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:07-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:49:59-debug: refresh db internal success
2025-9-17 10:49:59-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:59-debug: refresh db assets success
2025-9-17 10:49:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:49:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:49:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:49:59-debug: asset-db:refresh-all-database (139ms)
2025-9-17 10:49:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:49:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:59-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:50:08-debug: refresh db internal success
2025-9-17 10:50:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:08-debug: refresh db assets success
2025-9-17 10:50:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:50:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:50:08-debug: asset-db:refresh-all-database (136ms)
2025-9-17 10:50:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:50:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:50:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:08-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:50:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:21-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:50:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:37-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 10:52:19-debug: refresh db internal success
2025-9-17 10:52:19-debug: refresh db assets success
2025-9-17 10:52:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:52:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:52:19-debug: asset-db:refresh-all-database (127ms)
2025-9-17 10:53:24-debug: refresh db internal success
2025-9-17 10:53:24-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:53:24-debug: refresh db assets success
2025-9-17 10:53:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:53:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:53:24-debug: asset-db:refresh-all-database (140ms)
2025-9-17 10:53:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:53:24-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:54:01-debug: refresh db internal success
2025-9-17 10:54:01-debug: refresh db assets success
2025-9-17 10:54:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:01-debug: asset-db:refresh-all-database (102ms)
2025-9-17 10:54:35-debug: refresh db internal success
2025-9-17 10:54:35-debug: refresh db assets success
2025-9-17 10:54:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:35-debug: asset-db:refresh-all-database (138ms)
2025-9-17 10:54:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:54:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 10:54:58-debug: refresh db internal success
2025-9-17 10:54:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:58-debug: refresh db assets success
2025-9-17 10:54:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:58-debug: asset-db:refresh-all-database (125ms)
2025-9-17 10:55:37-debug: refresh db internal success
2025-9-17 10:55:37-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:55:37-debug: refresh db assets success
2025-9-17 10:55:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:55:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:55:37-debug: asset-db:refresh-all-database (140ms)
2025-9-17 10:55:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:55:37-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 10:56:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:56:09-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 10:57:47-debug: refresh db internal success
2025-9-17 10:57:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:57:47-debug: refresh db assets success
2025-9-17 10:57:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:57:47-debug: asset-db:refresh-all-database (106ms)
2025-9-17 10:58:00-debug: refresh db internal success
2025-9-17 10:58:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\icons\trigger.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:58:00-debug: %cReImport%c: E:\M2Game\Client\assets\editor\icons\trigger.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:58:00-debug: refresh db assets success
2025-9-17 10:58:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:00-debug: asset-db:refresh-all-database (143ms)
2025-9-17 10:58:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:58:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 10:58:09-debug: refresh db internal success
2025-9-17 10:58:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:09-debug: refresh db assets success
2025-9-17 10:58:09-debug: asset-db:refresh-all-database (106ms)
2025-9-17 10:58:27-debug: refresh db internal success
2025-9-17 10:58:27-debug: refresh db assets success
2025-9-17 10:58:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:58:27-debug: asset-db:refresh-all-database (110ms)
2025-9-17 10:58:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:58:43-debug: refresh db internal success
2025-9-17 10:58:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:43-debug: refresh db assets success
2025-9-17 10:58:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:43-debug: asset-db:refresh-all-database (126ms)
2025-9-17 10:59:45-debug: refresh db internal success
2025-9-17 10:59:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:59:45-debug: refresh db assets success
2025-9-17 10:59:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:59:45-debug: asset-db:refresh-all-database (111ms)
2025-9-17 11:01:22-debug: refresh db internal success
2025-9-17 11:01:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:01:22-debug: refresh db assets success
2025-9-17 11:01:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:01:22-debug: asset-db:refresh-all-database (102ms)
2025-9-17 11:01:22-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-17 11:01:22-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-17 11:02:38-debug: refresh db internal success
2025-9-17 11:02:38-debug: refresh db assets success
2025-9-17 11:02:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:02:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:02:38-debug: asset-db:refresh-all-database (127ms)
2025-9-17 11:03:18-debug: refresh db internal success
2025-9-17 11:03:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:03:18-debug: refresh db assets success
2025-9-17 11:03:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:03:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:03:18-debug: asset-db:refresh-all-database (136ms)
2025-9-17 11:03:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:03:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:03:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:03:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 11:03:59-debug: refresh db internal success
2025-9-17 11:03:59-debug: refresh db assets success
2025-9-17 11:03:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:03:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:03:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:03:59-debug: asset-db:refresh-all-database (109ms)
2025-9-17 11:03:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:05:28-debug: refresh db internal success
2025-9-17 11:05:28-debug: refresh db assets success
2025-9-17 11:05:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:05:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:05:28-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:05:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:05:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:06:11-debug: refresh db internal success
2025-9-17 11:06:11-debug: refresh db assets success
2025-9-17 11:06:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:06:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:06:11-debug: asset-db:refresh-all-database (108ms)
2025-9-17 11:07:07-debug: refresh db internal success
2025-9-17 11:07:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:07-debug: refresh db assets success
2025-9-17 11:07:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:07-debug: asset-db:refresh-all-database (104ms)
2025-9-17 11:07:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:08-debug: refresh db internal success
2025-9-17 11:07:08-debug: refresh db assets success
2025-9-17 11:07:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:08-debug: asset-db:refresh-all-database (109ms)
2025-9-17 11:07:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:10-debug: refresh db internal success
2025-9-17 11:07:10-debug: refresh db assets success
2025-9-17 11:07:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:10-debug: asset-db:refresh-all-database (98ms)
2025-9-17 11:07:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:24-debug: refresh db internal success
2025-9-17 11:07:24-debug: refresh db assets success
2025-9-17 11:07:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:24-debug: asset-db:refresh-all-database (107ms)
2025-9-17 11:07:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:08:06-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:08:06-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:08:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:08:11-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 11:10:48-debug: refresh db internal success
2025-9-17 11:10:48-debug: refresh db assets success
2025-9-17 11:10:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:10:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:10:48-debug: asset-db:refresh-all-database (127ms)
2025-9-17 11:13:14-debug: refresh db internal success
2025-9-17 11:13:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:14-debug: refresh db assets success
2025-9-17 11:13:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:13:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:13:14-debug: asset-db:refresh-all-database (141ms)
2025-9-17 11:13:14-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 11:13:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 11:13:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:15-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 11:13:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:15-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 11:13:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:29-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:13:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:42-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 11:15:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:15:38-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 11:15:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:15:56-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (1ms)
2025-9-17 11:16:08-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab...
2025-9-17 11:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:16:08-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave success
2025-9-17 11:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:16:08-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:17:38-debug: refresh db internal success
2025-9-17 11:17:38-debug: refresh db assets success
2025-9-17 11:17:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:17:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:17:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:17:38-debug: asset-db:refresh-all-database (114ms)
2025-9-17 11:17:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:17:41-debug: refresh db internal success
2025-9-17 11:17:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:17:41-debug: refresh db assets success
2025-9-17 11:17:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:17:41-debug: asset-db:refresh-all-database (104ms)
2025-9-17 11:17:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:17:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 11:23:07-debug: refresh db internal success
2025-9-17 11:23:08-debug: refresh db assets success
2025-9-17 11:23:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:23:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:23:08-debug: asset-db:refresh-all-database (128ms)
2025-9-17 11:23:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:27:18-debug: refresh db internal success
2025-9-17 11:27:19-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:19-debug: refresh db assets success
2025-9-17 11:27:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:27:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:27:19-debug: asset-db:refresh-all-database (147ms)
2025-9-17 11:27:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:27:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:27:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 11:27:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:52-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:27:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:55-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (3ms)
2025-9-17 11:28:24-debug: refresh db internal success
2025-9-17 11:28:24-debug: refresh db assets success
2025-9-17 11:28:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:28:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:28:24-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:28:30-debug: refresh db internal success
2025-9-17 11:28:30-debug: refresh db assets success
2025-9-17 11:28:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:28:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:28:30-debug: asset-db:refresh-all-database (100ms)
2025-9-17 11:40:07-debug: refresh db internal success
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: refresh db assets success
2025-9-17 11:40:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:07-debug: asset-db:refresh-all-database (168ms)
2025-9-17 11:40:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:40:34-debug: refresh db internal success
2025-9-17 11:40:34-debug: refresh db assets success
2025-9-17 11:40:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:34-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:40:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:40:39-debug: refresh db internal success
2025-9-17 11:40:39-debug: refresh db assets success
2025-9-17 11:40:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:39-debug: asset-db:refresh-all-database (115ms)
2025-9-17 11:40:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:54:58-debug: refresh db internal success
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:54:58-debug: refresh db assets success
2025-9-17 11:54:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:54:58-debug: asset-db:refresh-all-database (138ms)
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:55:14-debug: refresh db internal success
2025-9-17 11:55:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:55:14-debug: refresh db assets success
2025-9-17 11:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:55:14-debug: asset-db:refresh-all-database (106ms)
2025-9-17 11:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:55:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:55:14-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (35ms)
2025-9-17 11:57:17-debug: refresh db internal success
2025-9-17 11:57:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:17-debug: refresh db assets success
2025-9-17 11:57:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:17-debug: asset-db:refresh-all-database (133ms)
2025-9-17 11:57:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:57:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:57:32-debug: refresh db internal success
2025-9-17 11:57:33-debug: refresh db assets success
2025-9-17 11:57:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:33-debug: asset-db:refresh-all-database (102ms)
2025-9-17 11:57:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:57:34-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-17 11:57:40-debug: refresh db internal success
2025-9-17 11:57:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:40-debug: refresh db assets success
2025-9-17 11:57:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:40-debug: asset-db:refresh-all-database (106ms)
2025-9-17 11:57:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:57:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:58:34-debug: refresh db internal success
2025-9-17 11:58:34-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\randTerrain
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\EmittierTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\randTerrain\RandTerrain.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\RandTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss\BossPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: refresh db assets success
2025-9-17 11:58:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:58:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:58:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:58:34-debug: asset-db:refresh-all-database (175ms)
2025-9-17 11:58:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:58:36-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:36-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:58:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:37-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 12:02:25-debug: refresh db internal success
2025-9-17 12:02:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:02:25-debug: refresh db assets success
2025-9-17 12:02:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:02:25-debug: asset-db:refresh-all-database (136ms)
2025-9-17 12:09:18-debug: refresh db internal success
2025-9-17 12:09:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:18-debug: refresh db assets success
2025-9-17 12:09:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:09:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:09:18-debug: asset-db:refresh-all-database (120ms)
2025-9-17 12:09:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 12:09:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 12:09:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 12:09:30-debug: refresh db internal success
2025-9-17 12:09:31-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:09:31-debug: refresh db assets success
2025-9-17 12:09:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:09:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 12:09:31-debug: asset-db:refresh-all-database (127ms)
2025-9-17 12:09:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 12:09:31-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:31-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 12:09:42-debug: refresh db internal success
2025-9-17 12:09:42-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:42-debug: refresh db assets success
2025-9-17 12:09:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:09:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:09:42-debug: asset-db:refresh-all-database (105ms)
2025-9-17 12:09:43-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:43-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 12:09:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:09:45-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-17 12:09:45-debug: refresh db internal success
2025-9-17 12:09:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:09:45-debug: refresh db assets success
2025-9-17 12:09:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:09:45-debug: asset-db:refresh-all-database (101ms)
2025-9-17 12:10:08-debug: refresh db internal success
2025-9-17 12:10:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:10:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:10:08-debug: refresh db assets success
2025-9-17 12:10:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:10:08-debug: asset-db:refresh-all-database (105ms)
2025-9-17 12:10:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:10:09-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 12:11:38-debug: refresh db internal success
2025-9-17 12:11:38-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:11:38-debug: refresh db assets success
2025-9-17 12:11:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:11:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:11:38-debug: asset-db:refresh-all-database (157ms)
2025-9-17 12:11:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 12:11:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 12:11:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:11:38-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 12:13:06-debug: refresh db internal success
2025-9-17 12:13:06-debug: refresh db assets success
2025-9-17 12:13:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:13:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:13:06-debug: asset-db:refresh-all-database (131ms)
2025-9-17 12:15:10-debug: refresh db internal success
2025-9-17 12:15:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:15:10-debug: refresh db assets success
2025-9-17 12:15:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:15:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:15:10-debug: asset-db:refresh-all-database (142ms)
2025-9-17 12:15:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 12:15:10-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:15:10-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 12:15:50-debug: refresh db internal success
2025-9-17 12:15:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:15:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:15:50-debug: refresh db assets success
2025-9-17 12:15:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:15:50-debug: asset-db:refresh-all-database (137ms)
2025-9-17 12:15:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:15:51-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 12:15:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 12:15:54-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 12:16:04-debug: refresh db internal success
2025-9-17 12:16:04-debug: refresh db assets success
2025-9-17 12:16:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:16:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:16:04-debug: asset-db:refresh-all-database (100ms)
2025-9-17 12:16:05-debug: refresh db internal success
2025-9-17 12:16:06-debug: refresh db assets success
2025-9-17 12:16:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:16:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:16:06-debug: asset-db:refresh-all-database (97ms)
2025-9-17 12:17:28-debug: refresh db internal success
2025-9-17 12:17:28-debug: refresh db assets success
2025-9-17 12:17:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:17:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:17:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 12:17:28-debug: asset-db:refresh-all-database (113ms)
2025-9-17 12:17:28-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 15:33:25-debug: refresh db internal success
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\SettlementUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\StatisticsHurtCell.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\StatisticsScoreCell.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\StatisticsUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\SettlementUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\StatisticsHurtCell.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\StatisticsScoreCell.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\StatisticsUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\MessageBox.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TextUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\AnnouncementUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\TextUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:25-debug: refresh db assets success
2025-9-17 15:33:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:33:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:33:25-debug: asset-db:refresh-all-database (353ms)
2025-9-17 15:33:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 15:33:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:26-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 15:33:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:27-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (2ms)
2025-9-17 15:33:27-debug: refresh db internal success
2025-9-17 15:33:27-debug: refresh db assets success
2025-9-17 15:33:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:33:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:33:27-debug: asset-db:refresh-all-database (155ms)
2025-9-17 15:33:27-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-17 15:33:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:33:27-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 15:34:04-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab...
2025-9-17 15:34:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:34:04-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave success
2025-9-17 15:34:04-debug: refresh db internal success
2025-9-17 15:34:04-debug: refresh db assets success
2025-9-17 15:34:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:34:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:34:04-debug: asset-db:refresh-all-database (135ms)
2025-9-17 15:34:04-debug: asset-db:worker-effect-data-processing (-2ms)
2025-9-17 15:34:04-debug: asset-db-hook-engine-extends-afterRefresh (-2ms)
2025-9-17 15:34:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:34:04-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (1ms)
2025-9-17 15:40:34-debug: refresh db internal success
2025-9-17 15:40:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:40:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:40:34-debug: refresh db assets success
2025-9-17 15:40:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:40:34-debug: asset-db:refresh-all-database (153ms)
2025-9-17 15:40:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 15:40:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:40:35-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 15:42:04-debug: refresh db internal success
2025-9-17 15:42:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:42:04-debug: refresh db assets success
2025-9-17 15:42:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:42:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:42:04-debug: asset-db:refresh-all-database (137ms)
2025-9-17 15:42:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 15:42:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 15:42:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:42:04-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 15:42:13-debug: refresh db internal success
2025-9-17 15:42:13-debug: refresh db assets success
2025-9-17 15:42:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:42:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:42:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 15:42:13-debug: asset-db:refresh-all-database (101ms)
2025-9-17 15:42:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 15:42:31-debug: refresh db internal success
2025-9-17 15:42:31-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:42:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:42:31-debug: refresh db assets success
2025-9-17 15:42:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:42:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 15:42:31-debug: asset-db:refresh-all-database (132ms)
2025-9-17 15:42:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 15:42:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:42:32-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 15:57:46-debug: refresh db internal success
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane\StateMachine.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const\AttributeConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\AttributeData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill\SkillComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:47-debug: refresh db assets success
2025-9-17 15:57:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:57:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:57:47-debug: asset-db:refresh-all-database (162ms)
2025-9-17 15:57:47-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 15:57:47-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 15:57:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:48-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (42ms)
2025-9-17 15:57:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 15:57:49-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 15:57:56-debug: refresh db internal success
2025-9-17 15:57:56-debug: refresh db assets success
2025-9-17 15:57:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 15:57:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 15:57:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 15:57:56-debug: asset-db:refresh-all-database (110ms)
2025-9-17 15:57:56-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 16:00:32-debug: refresh db internal success
2025-9-17 16:00:32-debug: refresh db assets success
2025-9-17 16:00:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:00:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:00:32-debug: asset-db:refresh-all-database (128ms)
2025-9-17 16:00:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:00:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:00:50-debug: refresh db internal success
2025-9-17 16:00:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:00:50-debug: refresh db assets success
2025-9-17 16:00:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:00:50-debug: asset-db:refresh-all-database (136ms)
2025-9-17 16:20:08-debug: refresh db internal success
2025-9-17 16:20:08-debug: refresh db assets success
2025-9-17 16:20:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:20:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:20:08-debug: asset-db:refresh-all-database (151ms)
2025-9-17 16:20:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:20:24-debug: refresh db internal success
2025-9-17 16:20:25-debug: refresh db assets success
2025-9-17 16:20:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:20:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:20:25-debug: asset-db:refresh-all-database (104ms)
2025-9-17 16:28:48-debug: refresh db internal success
2025-9-17 16:28:48-debug: refresh db assets success
2025-9-17 16:28:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:28:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:28:48-debug: asset-db:refresh-all-database (128ms)
2025-9-17 16:28:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:30:13-debug: refresh db internal success
2025-9-17 16:30:13-debug: refresh db assets success
2025-9-17 16:30:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:30:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:30:13-debug: asset-db:refresh-all-database (128ms)
2025-9-17 16:31:02-debug: refresh db internal success
2025-9-17 16:31:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:31:02-debug: refresh db assets success
2025-9-17 16:31:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:31:02-debug: asset-db:refresh-all-database (112ms)
2025-9-17 16:31:50-debug: refresh db internal success
2025-9-17 16:31:50-debug: refresh db assets success
2025-9-17 16:31:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:31:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:31:50-debug: asset-db:refresh-all-database (126ms)
2025-9-17 16:31:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:31:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:40:04-debug: refresh db internal success
2025-9-17 16:40:04-debug: refresh db assets success
2025-9-17 16:40:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:40:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:40:04-debug: asset-db:refresh-all-database (126ms)
2025-9-17 16:40:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:40:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:40:13-debug: refresh db internal success
2025-9-17 16:40:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:40:13-debug: refresh db assets success
2025-9-17 16:40:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:40:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:40:13-debug: asset-db:refresh-all-database (107ms)
2025-9-17 16:40:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:54:10-debug: refresh db internal success
2025-9-17 16:54:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:54:10-debug: refresh db assets success
2025-9-17 16:54:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:54:10-debug: asset-db:refresh-all-database (153ms)
2025-9-17 16:54:11-debug: refresh db internal success
2025-9-17 16:54:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:54:11-debug: refresh db assets success
2025-9-17 16:54:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:54:11-debug: asset-db:refresh-all-database (124ms)
2025-9-17 16:54:30-debug: refresh db internal success
2025-9-17 16:54:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:54:30-debug: refresh db assets success
2025-9-17 16:54:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:54:30-debug: asset-db:refresh-all-database (102ms)
2025-9-17 16:56:33-debug: refresh db internal success
2025-9-17 16:56:33-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:56:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:56:33-debug: refresh db assets success
2025-9-17 16:56:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:56:33-debug: asset-db:refresh-all-database (105ms)
2025-9-17 16:56:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:56:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:56:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:56:33-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 16:56:57-debug: refresh db internal success
2025-9-17 16:56:57-debug: refresh db assets success
2025-9-17 16:56:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:56:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:56:57-debug: asset-db:refresh-all-database (101ms)
2025-9-17 16:56:59-debug: refresh db internal success
2025-9-17 16:56:59-debug: refresh db assets success
2025-9-17 16:56:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:56:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:56:59-debug: asset-db:refresh-all-database (114ms)
2025-9-17 16:57:05-debug: refresh db internal success
2025-9-17 16:57:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:57:05-debug: refresh db assets success
2025-9-17 16:57:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:57:05-debug: asset-db:refresh-all-database (100ms)
2025-9-17 16:57:08-debug: refresh db internal success
2025-9-17 16:57:08-debug: refresh db assets success
2025-9-17 16:57:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:57:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:57:08-debug: asset-db:refresh-all-database (104ms)
2025-9-17 16:57:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:57:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:57:10-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-17 16:58:09-debug: refresh db internal success
2025-9-17 16:58:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:58:09-debug: refresh db assets success
2025-9-17 16:58:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:58:09-debug: asset-db:refresh-all-database (133ms)
2025-9-17 16:58:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:58:47-debug: refresh db internal success
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\GamePauseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\game\GamePauseUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresskill.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestask.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\GameInUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:47-debug: refresh db assets success
2025-9-17 16:58:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:58:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:58:47-debug: asset-db:refresh-all-database (183ms)
2025-9-17 16:58:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 16:58:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 16:58:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:48-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 16:58:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 16:58:49-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 16:58:52-debug: refresh db internal success
2025-9-17 16:58:52-debug: refresh db assets success
2025-9-17 16:58:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:58:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:58:52-debug: asset-db:refresh-all-database (135ms)
2025-9-17 16:59:21-debug: refresh db internal success
2025-9-17 16:59:21-debug: refresh db assets success
2025-9-17 16:59:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 16:59:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 16:59:21-debug: asset-db:refresh-all-database (140ms)
2025-9-17 17:02:31-debug: refresh db internal success
2025-9-17 17:02:31-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:02:31-debug: refresh db assets success
2025-9-17 17:02:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:02:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:02:31-debug: asset-db:refresh-all-database (131ms)
2025-9-17 17:02:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:02:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:02:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:02:32-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 17:02:40-debug: refresh db internal success
2025-9-17 17:02:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:02:40-debug: refresh db assets success
2025-9-17 17:02:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:02:40-debug: asset-db:refresh-all-database (102ms)
2025-9-17 17:02:40-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-17 17:02:40-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-17 17:06:34-debug: refresh db internal success
2025-9-17 17:06:34-debug: refresh db assets success
2025-9-17 17:06:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:06:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:06:34-debug: asset-db:refresh-all-database (154ms)
2025-9-17 17:06:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:06:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:07:21-debug: refresh db internal success
2025-9-17 17:07:21-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:07:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:07:21-debug: refresh db assets success
2025-9-17 17:07:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:07:21-debug: asset-db:refresh-all-database (155ms)
2025-9-17 17:07:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:07:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:07:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:07:21-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (17ms)
2025-9-17 17:07:28-debug: refresh db internal success
2025-9-17 17:07:28-debug: refresh db assets success
2025-9-17 17:07:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:07:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:07:28-debug: asset-db:refresh-all-database (107ms)
2025-9-17 17:07:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:07:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:08:53-debug: refresh db internal success
2025-9-17 17:08:53-debug: refresh db assets success
2025-9-17 17:08:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:08:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:08:53-debug: asset-db:refresh-all-database (116ms)
2025-9-17 17:08:53-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 17:08:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:12:52-debug: refresh db internal success
2025-9-17 17:12:52-debug: refresh db assets success
2025-9-17 17:12:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:12:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:12:52-debug: asset-db:refresh-all-database (107ms)
2025-9-17 17:12:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:12:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:13:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:13:00-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 17:13:08-debug: refresh db internal success
2025-9-17 17:13:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:13:08-debug: refresh db assets success
2025-9-17 17:13:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:13:08-debug: asset-db:refresh-all-database (105ms)
2025-9-17 17:13:18-debug: refresh db internal success
2025-9-17 17:13:18-debug: refresh db assets success
2025-9-17 17:13:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:13:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:13:18-debug: asset-db:refresh-all-database (105ms)
2025-9-17 17:13:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:13:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:13:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\enemy\EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:13:54-debug: asset-db:reimport-assetf22ea656-6b22-4569-95bf-8be5766bba40 (2ms)
2025-9-17 17:14:12-debug: refresh db internal success
2025-9-17 17:14:12-debug: refresh db assets success
2025-9-17 17:14:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:14:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:14:12-debug: asset-db:refresh-all-database (141ms)
2025-9-17 17:14:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:14:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:14:31-debug: refresh db internal success
2025-9-17 17:14:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:14:32-debug: refresh db assets success
2025-9-17 17:14:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:14:32-debug: asset-db:refresh-all-database (146ms)
2025-9-17 17:14:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:14:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:16:47-debug: refresh db internal success
2025-9-17 17:16:47-debug: refresh db assets success
2025-9-17 17:16:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:16:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:16:47-debug: asset-db:refresh-all-database (154ms)
2025-9-17 17:16:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:16:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:17:13-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:17:13-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 17:17:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:17:37-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 17:17:38-debug: refresh db internal success
2025-9-17 17:17:38-debug: refresh db assets success
2025-9-17 17:17:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:17:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:17:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:17:38-debug: asset-db:refresh-all-database (111ms)
2025-9-17 17:17:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:17:39-debug: refresh db internal success
2025-9-17 17:17:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:17:39-debug: refresh db assets success
2025-9-17 17:17:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:17:39-debug: asset-db:refresh-all-database (106ms)
2025-9-17 17:17:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:17:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:17:46-debug: refresh db internal success
2025-9-17 17:17:46-debug: refresh db assets success
2025-9-17 17:17:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:17:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:17:46-debug: asset-db:refresh-all-database (104ms)
2025-9-17 17:17:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:17:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:17:51-debug: Query all assets info in project
2025-9-17 17:17:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:17:51-debug: Skip compress image, progress: 0%
2025-9-17 17:17:51-debug: Init all bundles start..., progress: 0%
2025-9-17 17:17:51-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:17:51-debug:   Number of all scenes: 9
2025-9-17 17:17:51-debug:   Number of all scripts: 235
2025-9-17 17:17:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:17:51-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:211.92MB, end 210.31MB, increase: -1644.93KB
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:17:51-debug:   Number of other assets: 1804
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:210.34MB, end 210.60MB, increase: 263.20KB
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-17 17:17:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.63MB, end 210.65MB, increase: 26.95KB
2025-9-17 17:17:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:17:51-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.68MB, end 210.71MB, increase: 26.52KB
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-17 17:17:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.74MB, end 211.03MB, increase: 300.07KB
2025-9-17 17:17:51-debug: Query all assets info in project
2025-9-17 17:17:51-debug: Query all assets info in project
2025-9-17 17:17:51-debug: Query all assets info in project
2025-9-17 17:17:51-debug: Query all assets info in project
2025-9-17 17:17:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:17:51-debug: Skip compress image, progress: 0%
2025-9-17 17:17:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:17:51-debug: Skip compress image, progress: 0%
2025-9-17 17:17:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:17:51-debug: Skip compress image, progress: 0%
2025-9-17 17:17:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:17:51-debug: Skip compress image, progress: 0%
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:17:51-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:17:51-debug: Init all bundles start..., progress: 0%
2025-9-17 17:17:51-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: Init all bundles start..., progress: 0%
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:17:51-debug: Init all bundles start..., progress: 0%
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:17:51-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:17:51-debug: Init all bundles start..., progress: 0%
2025-9-17 17:17:51-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:17:51-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:17:51-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:17:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:17:51-debug:   Number of all scenes: 9
2025-9-17 17:17:51-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:17:51-debug:   Number of all scripts: 235
2025-9-17 17:17:51-debug:   Number of other assets: 1804
2025-9-17 17:17:51-debug:   Number of all scenes: 9
2025-9-17 17:17:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:17:51-debug:   Number of all scripts: 235
2025-9-17 17:17:51-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:17:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:17:51-debug:   Number of other assets: 1804
2025-9-17 17:17:51-debug:   Number of other assets: 1804
2025-9-17 17:17:51-debug:   Number of all scripts: 235
2025-9-17 17:17:51-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:17:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:17:51-debug:   Number of all scenes: 9
2025-9-17 17:17:51-debug:   Number of all scenes: 9
2025-9-17 17:17:51-debug:   Number of other assets: 1804
2025-9-17 17:17:51-debug:   Number of all scripts: 235
2025-9-17 17:17:51-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:211.36MB, end 216.50MB, increase: 5.14MB
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:216.53MB, end 216.54MB, increase: 12.09KB
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:216.57MB, end 216.58MB, increase: 12.52KB
2025-9-17 17:17:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:216.62MB, end 216.63MB, increase: 12.58KB
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:17:51-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-17 17:17:51-debug: [Build Memory track]: 查询 Asset Bundle start:216.66MB, end 217.63MB, increase: 994.35KB
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:17:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.77MB, end 217.80MB, increase: 32.41KB
2025-9-17 17:17:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:17:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:17:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:17:51-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.96MB, end 217.98MB, increase: 22.11KB
2025-9-17 17:17:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:17:51-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-17 17:17:51-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:17:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:17:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-17 17:17:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.14MB, end 219.22MB, increase: 1.09MB
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:17:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-17 17:19:35-debug: refresh db internal success
2025-9-17 17:19:35-debug: refresh db assets success
2025-9-17 17:19:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:19:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:19:35-debug: asset-db:refresh-all-database (130ms)
2025-9-17 17:19:51-debug: refresh db internal success
2025-9-17 17:19:51-debug: refresh db assets success
2025-9-17 17:19:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:19:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:19:51-debug: asset-db:refresh-all-database (107ms)
2025-9-17 17:19:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:19:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:19:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:19:52-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 17:19:53-debug: refresh db internal success
2025-9-17 17:19:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:19:53-debug: refresh db assets success
2025-9-17 17:19:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:19:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:19:53-debug: asset-db:refresh-all-database (103ms)
2025-9-17 17:19:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:21:18-debug: refresh db internal success
2025-9-17 17:21:18-debug: refresh db assets success
2025-9-17 17:21:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:21:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:21:18-debug: asset-db:refresh-all-database (106ms)
2025-9-17 17:21:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:21:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:21:21-debug: refresh db internal success
2025-9-17 17:21:21-debug: refresh db assets success
2025-9-17 17:21:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:21:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:21:21-debug: asset-db:refresh-all-database (104ms)
2025-9-17 17:21:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:21:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:21:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:21:27-debug: asset-db:reimport-assetf5e34a8e-0631-42fa-a803-961da4598cdd (2ms)
2025-9-17 17:22:39-debug: refresh db internal success
2025-9-17 17:22:39-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorWaveParam.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:22:39-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:22:39-debug: refresh db assets success
2025-9-17 17:22:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:22:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:22:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:22:39-debug: asset-db:refresh-all-database (141ms)
2025-9-17 17:22:39-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:22:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:22:40-debug: asset-db:reimport-assetf5e34a8e-0631-42fa-a803-961da4598cdd (3ms)
2025-9-17 17:22:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:22:57-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (4ms)
2025-9-17 17:22:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:22:57-debug: asset-db:reimport-assetf5e34a8e-0631-42fa-a803-961da4598cdd (3ms)
2025-9-17 17:22:57-debug: refresh db internal success
2025-9-17 17:22:57-debug: refresh db assets success
2025-9-17 17:22:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:22:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:22:57-debug: asset-db:refresh-all-database (105ms)
2025-9-17 17:22:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:22:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:22:58-debug: Query all assets info in project
2025-9-17 17:22:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-17 17:22:58-debug: Skip compress image, progress: 0%
2025-9-17 17:22:58-debug: Init all bundles start..., progress: 0%
2025-9-17 17:22:58-debug: Num of bundles: 16..., progress: 0%
2025-9-17 17:22:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:22:58-debug: 查询 Asset Bundle start, progress: 0%
2025-9-17 17:22:58-debug: Init bundle root assets start..., progress: 0%
2025-9-17 17:22:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-17 17:22:58-debug:   Number of all scenes: 9
2025-9-17 17:22:58-debug:   Number of all scripts: 235
2025-9-17 17:22:58-debug: Init bundle root assets success..., progress: 0%
2025-9-17 17:22:58-debug:   Number of other assets: 1804
2025-9-17 17:22:58-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-17 17:22:58-debug: [Build Memory track]: 查询 Asset Bundle start:213.86MB, end 215.42MB, increase: 1.57MB
2025-9-17 17:22:58-debug: 查询 Asset Bundle start, progress: 5%
2025-9-17 17:22:58-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-17 17:22:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-17 17:22:58-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-17 17:22:58-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-17 17:22:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-17 17:22:58-debug: [Build Memory track]: 查询 Asset Bundle start:215.45MB, end 215.70MB, increase: 257.63KB
2025-9-17 17:22:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:22:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-17 17:22:58-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-17 17:22:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.73MB, end 215.76MB, increase: 35.85KB
2025-9-17 17:22:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-17 17:22:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-17 17:22:58-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-17 17:22:58-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-17 17:22:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.79MB, end 215.82MB, increase: 26.16KB
2025-9-17 17:22:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-17 17:22:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-17 17:22:58-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-17 17:22:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.85MB, end 216.15MB, increase: 305.43KB
2025-9-17 17:22:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-17 17:23:48-debug: refresh db internal success
2025-9-17 17:23:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:23:48-debug: refresh db assets success
2025-9-17 17:23:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:23:48-debug: asset-db:refresh-all-database (132ms)
2025-9-17 17:23:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:23:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 17:24:05-debug: refresh db internal success
2025-9-17 17:24:05-debug: refresh db assets success
2025-9-17 17:24:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:24:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:24:05-debug: asset-db:refresh-all-database (109ms)
2025-9-17 17:24:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 17:24:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:24:06-debug: refresh db internal success
2025-9-17 17:24:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:24:07-debug: refresh db assets success
2025-9-17 17:24:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:24:07-debug: asset-db:refresh-all-database (107ms)
2025-9-17 17:24:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:24:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:24:13-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 17:24:13-debug: asset-db:reimport-assetf5e34a8e-0631-42fa-a803-961da4598cdd (3ms)
2025-9-17 17:24:20-debug: refresh db internal success
2025-9-17 17:24:20-debug: refresh db assets success
2025-9-17 17:24:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:24:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:24:20-debug: asset-db:refresh-all-database (115ms)
2025-9-17 17:24:20-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 17:24:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 17:24:55-debug: refresh db internal success
2025-9-17 17:24:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 17:24:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 17:24:55-debug: refresh db assets success
2025-9-17 17:24:55-debug: asset-db:refresh-all-database (107ms)
2025-9-17 17:24:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 17:24:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
