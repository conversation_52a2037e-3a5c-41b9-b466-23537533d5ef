{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts"], "names": ["_decorator", "Component", "Node", "EventManager", "GameEvent", "GameIns", "UIMgr", "GamePauseUI", "ccclass", "property", "GameInUI", "onEnable", "tipNode", "active", "btnPause", "Instance", "on", "GameStart", "onEventGameStart", "onDisable", "off", "setTouchState", "is<PERSON><PERSON>ch", "onBtnPauseClicked", "gameRuleManager", "gamePause", "openUI"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACzBC,MAAAA,Y;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;0BAGjBU,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACP,IAAD,C,2BALb,MACaQ,QADb,SAC8BT,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAO1BU,QAAAA,QAAQ,GAAS;AACvB,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA,eAAKC,QAAL,CAAeD,MAAf,GAAwB,KAAxB;AACA;AAAA;AAAA,4CAAaE,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,SAAnC,EAA8C,KAAKC,gBAAnD,EAAqE,IAArE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBK,GAAtB,CAA0B;AAAA;AAAA,sCAAUH,SAApC,EAA+C,KAAKC,gBAApD,EAAsE,IAAtE;AACH;;AAEDA,QAAAA,gBAAgB,GAAG;AACf,eAAKN,OAAL,CAAcC,MAAd,GAAuB,KAAvB;AACH;;AAGDQ,QAAAA,aAAa,CAACC,OAAD,EAAmB;AAC5B,cAAI,KAAKR,QAAT,EAAkB;AACd,iBAAKA,QAAL,CAAcD,MAAd,GAAuB,CAACS,OAAxB;AACH;AACJ;;AAEDC,QAAAA,iBAAiB,GAAG;AAChB;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,SAAxB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AA/BmC,O;;;;;iBAGb,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport EventManager from '../../../event/EventManager';\r\nimport { GameEvent } from '../../event/GameEvent';\r\nimport { GameIns } from '../../GameIns';\r\nimport { UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GamePauseUI } from '../../../ui/gameui/game/GamePauseUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameInUI')\r\nexport class GameInUI extends Component {\r\n\r\n    @property(Node)\r\n    tipNode: Node | null = null\r\n    @property(Node)\r\n    btnPause: Node | null = null\r\n\r\n    protected onEnable(): void {\r\n        this.tipNode!.active = true;\r\n        this.btnPause!.active = false;\r\n        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);\r\n    }\r\n\r\n    onEventGameStart() {\r\n        this.tipNode!.active = false;\r\n    }\r\n\r\n\r\n    setTouchState(isTouch: boolean) {\r\n        if (this.btnPause){\r\n            this.btnPause.active = !isTouch;\r\n        }\r\n    }\r\n\r\n    onBtnPauseClicked() {\r\n        GameIns.gameRuleManager.gamePause();\r\n        UIMgr.openUI(GamePauseUI);\r\n    }\r\n}\r\n\r\n\r\n"]}