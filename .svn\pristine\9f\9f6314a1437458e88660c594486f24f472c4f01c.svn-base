import { error } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { ResPlane } from "db://assets/bundles/common/script/autogen/luban/schema";
import { AttributeConst } from "../../const/AttributeConst";
import { AttributeData } from "../base/AttributeData";

const PATH_SPINE = "spine/plane/"

export class PlaneData extends AttributeData {
    id: number = 0;//唯一id
    _planeId: number = 0;//飞机id
    _level: number = 0//英雄等级

    config: ResPlane | null = null;//飞机静态配置
    get level() {
        return this._level;
    }

    set level(value) {
        if (value != this._level) {
            this._level = value;
            // this.updateConfig();
        }
    }

    get planeId() {
        return this._planeId;
    }

    set planeId(value) {
        if (value != this._planeId) {
            this._planeId = value;
            this.updateConfig();
        }
    }

    get recourseSpine() {
        if (!this.config) {
            return "";
        }
        return PATH_SPINE + this.config.id + "/" + this.config.id;
    }

    updateConfig() {
        if (!this._planeId) {
            error("Plane id or level is 0, cannot update config.");
            return;
        }
        this.config = MyApp.lubanMgr.table.TbPlane.get(this._planeId)!;
        this.updateData();
    }

    updateData() {
        if (!this.config) {
            error("Plane lv config is null, cannot update attributes.");
            return;
        }
        this.setBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.property.MaxHP);
        this.setBaseAttribute(AttributeConst.AttackOutAdd, this.config.property.Attack);

        //根据飞机基础配置表，获取基础属性
        this.getAttributeList();
    }

    getAttributeList() {
        // 获取装备，技能，buff等属性
    }
}