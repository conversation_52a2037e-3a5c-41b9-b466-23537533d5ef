{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "CCFloat", "CCInteger", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "ccclass", "property", "executeInEditMode", "menu", "WaveTrack", "WaveTrackGroup", "Wave", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_nextSpawnIndex", "_spawnQueue", "_offsetX", "_offsetY", "_eventGroups", "_eventGroupContext", "isCompleted", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroup", "for<PERSON>ach", "group", "weight", "selfWeight", "reset", "length", "eventGroupData", "wave", "groupData", "push", "trigger", "x", "y", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "Math", "random", "planeID", "tick", "dtInMiliseconds", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "setPos", "initMove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;;;;;;;;OACnB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDb,U;;2BAG1Cc,S,WADZJ,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACV,OAAD,C,UAERU,QAAQ,CAACV,OAAD,C,UAERU,QAAQ,CAACV,OAAD,C,2BARb,MACaa,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZL,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACT,SAAD,C,WAERS,QAAQ,CAAC,CAACG,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAMpBC,I,aAHZN,OAAO,CAAC,MAAD,C,WACPG,IAAI,CAAC,OAAD,C,WACJD,iBAAiB,E,WAEbD,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,gEAJb,MAGaD,IAHb,SAG0Bb,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAIhC;AACJ;AACA;AANoC,eAOxBe,YAPwB,GAOA,KAPA;AAAA,eAUxBC,gBAVwB,GAUG,CAVH;AAAA,eAWxBC,cAXwB,GAWC,CAXD;AAAA,eAYxBC,YAZwB,GAYD,CAZC;AAahC;AAbgC,eAcxBC,kBAdwB,GAcK,CAdL;AAehC;AAfgC,eAgBxBC,eAhBwB,GAgBE,CAhBF;AAAA,eAiBxBC,WAjBwB,GAiBA,EAjBA;AAkBhC;AAlBgC,eAmBxBC,QAnBwB,GAmBL,CAnBK;AAAA,eAoBxBC,QApBwB,GAoBL,CApBK;AAqBhC;AArBgC,eAsBxBC,YAtBwB,GAsBS,EAtBT;AAAA,eAuBxBC,kBAvBwB,GAuByB,IAvBzB;AAAA;;AAQhC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKX,YAAZ;AAA2B;;AAgBtDY,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAL,IAAiB,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA9D,EAAsE;AAClE,iBAAKZ,YAAL,GAAoB,CAApB,CADkE,CAElE;;AACA,iBAAKU,QAAL,CAAcG,UAAd,CAAyBC,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKf,YAAL,IAAqBe,KAAK,CAACC,MAA3B;AACAD,cAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKjB,YAAxB;AACH,aAHD;AAIH;AACJ;;AAEOkB,QAAAA,KAAK,GAAG;AACZ,eAAKrB,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBgB,MAAjB,GAA0B,CAA1B,CALY,CAMZ;;AACA,eAAKb,YAAL,CAAkBa,MAAlB,GAA2B,CAA3B;;AACA,cAAI,KAAKT,QAAL,IAAiB,KAAKA,QAAL,CAAcU,cAAnC,EAAmD;AAC/C,gBAAI,CAAC,KAAKb,kBAAV,EAA8B;AAC1B,mBAAKA,kBAAL,GAA0B;AAAA;AAAA,mEAA1B;AACA,mBAAKA,kBAAL,CAAwBc,IAAxB,GAA+B,IAA/B;AACH;;AACD,iBAAKX,QAAL,CAAcU,cAAd,CAA6BN,OAA7B,CAAsCQ,SAAD,IAAe;AAChD,kBAAMP,KAAK,GAAG;AAAA;AAAA,oDAAmB,KAAKR,kBAAxB,EAA6Ce,SAA7C,CAAd;;AACA,mBAAKhB,YAAL,CAAkBiB,IAAlB,CAAuBR,KAAvB;AACH,aAHD;AAIH;AACJ;;AAEDS,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKR,KAAL;AACA,eAAKd,QAAL,GAAgBqB,CAAhB;AACA,eAAKpB,QAAL,GAAgBqB,CAAhB,CAH0B,CAK1B;;AACA,cAAI,KAAKhB,QAAT,EAAmB;AACf,iBAAKT,kBAAL,GAA0B,KAAKS,QAAL,CAAciB,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKlB,QAAL,CAAcmB,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKpB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9B,kBAAzB,EAA6C8B,CAAC,EAA9C,EAAkD;AAC9C,sBAAMC,YAAY,GAAGC,IAAI,CAACC,MAAL,KAAgB,KAAKlC,YAA1C;;AACA,uBAAK,IAAMe,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,wBAAImB,YAAY,IAAIjB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKd,WAAL,CAAiBoB,IAAjB,CAAsBR,KAAK,CAACoB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIJ,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAK9B,kBAAzB,EAA6C8B,EAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAK5B,WAAL,CAAiBoB,IAAjB,CAAsB,KAAKb,QAAL,CAAcG,UAAd,CAAyBkB,EAAC,GAAG,KAAKrB,QAAL,CAAcG,UAAd,CAAyBM,MAAtD,EAA8DgB,OAApF;AACH;AACJ;AACJ;AACJ;AACJ,SAnF+B,CAqFhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAKxC,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyBuC,eAAzB;;AACA,cAAI,KAAK3B,QAAL,CAAcmB,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKhC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAKuC,cAAL,EAAL,EAA4B;AACxB,qBAAKzC,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ,WAPD,MAQK;AACD;AACA,gBAAI,KAAKC,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKJ,YAAL,GAAoB,IAApB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKwC,cAAL;AACH;AACJ;AACJ;AACJ;;AAEOD,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAKpC,eAAL,IAAwB,KAAKC,WAAL,CAAiBgB,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKqB,oBAAL,CAA0B,KAAKtC,eAAL,EAA1B;AACA,eAAKH,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKY,QAAL,CAAc+B,aAAd,CAA4Bb,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOY,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKvC,WAAL,CAAiBgB,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIwB,QAAQ,GAAG,KAAKjC,QAAL,CAAciC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKlC,QAAL,CAAckC,UAAd,CAAyBhB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKiB,WAAL,CAAiB,KAAK1C,WAAL,CAAiBuC,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,cAAII,QAAQ,GAAG,KAAKjC,QAAL,CAAciC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKlC,QAAL,CAAckC,UAAd,CAAyBhB,IAAzB,EAAjB,CAF2B,CAG3B;;AAEA,cAAI,KAAKlB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,gBAAMoB,YAAY,GAAGC,IAAI,CAACC,MAAL,KAAgB,KAAKlC,YAA1C;;AACA,iBAAK,IAAMe,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,kBAAImB,YAAY,IAAIjB,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAK4B,WAAL,CAAiB9B,KAAK,CAACoB,OAAvB,EAAgCQ,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAKnC,QAAL,CAAcG,UAAd,CAAyB,KAAKX,eAAL,KAAyB,KAAKQ,QAAL,CAAcG,UAAd,CAAyBM,MAA3E,EAAmFgB,OAApG,EAA6GQ,QAA7G,EAAuHC,UAAvH;AACH;AACJ;;AAEaC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAAA;;AAAA;AACjE,gBAAIC,KAAK,GAAG;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,EAAuC,IAAvC,CAAZ;;AACA,gBAAIG,KAAJ,EAAW;AACP;AACA;AACA;AACAA,cAAAA,KAAK,CAACG,MAAN,CAAa,KAAI,CAAChD,QAAL,GAAgB2C,GAAG,CAACtB,CAAjC,EAAoC,KAAI,CAACpB,QAAL,GAAgB0C,GAAG,CAACrB,CAAxD;AACAuB,cAAAA,KAAK,CAACI,QAAN,CAAeL,KAAf;AACH;AARgE;AASpE;;AA/J+B,O;;;;;iBAEF;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的偏移位置\r\n    private _offsetX: number = 0;\r\n    private _offsetY: number = 0;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n\r\n    onLoad() {\r\n        if (this.waveData && this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._totalWeight = 0;\r\n            // add up _totalWeight if is random\r\n            this.waveData.spawnGroup.forEach((group) => {\r\n                this._totalWeight += group.weight;\r\n                group.selfWeight = this._totalWeight;\r\n            });\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        // this._spawnQueue = this.waveData.planeList;\r\n        this._eventGroups.length = 0;\r\n        if (this.waveData && this.waveData.eventGroupData) {\r\n            if (!this._eventGroupContext) {\r\n                this._eventGroupContext = new WaveEventGroupContext();\r\n                this._eventGroupContext.wave = this;\r\n            }\r\n            this.waveData.eventGroupData.forEach((groupData) => {\r\n                const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                this._eventGroups.push(group);\r\n            });\r\n        }\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._offsetX = x;\r\n        this._offsetY = y;\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = Math.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroup) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isCompleted = true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = Math.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroup) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private async createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        let enemy = GameIns.enemyManager.addPlane(planeId, null);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            // console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(this._offsetX + pos.x, this._offsetY + pos.y);\r\n            enemy.initMove(angle);\r\n        }\r\n    }\r\n\r\n}"]}