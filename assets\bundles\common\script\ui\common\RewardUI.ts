import { _decorator, Node, Tween, tween, Vec3 } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { ButtonPlus } from './components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass('RewardUI')
export class RewardUI extends BaseUI {
    @property(Node)
    nodeReward: Node | null = null;

    @property(ButtonPlus)
    btnGet: ButtonPlus | null = null;

    private activeTweens: Tween<Node>[] = [];
    private activeTimeouts: ReturnType<typeof setTimeout>[] = [];

    public static getUrl(): string { return "prefab/ui/RewardUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnGet!.addClick(this.onGetClick, this);
    }
    async onGetClick() {
        UIMgr.closeUI(RewardUI);
    }

    async onShow(rewardID: number): Promise<void> {
        this.showNodesSequentiallyWithScale();
    }

    private showNodesSequentiallyWithScale(): void {
        const children = this.nodeReward!.children;
        if (!children || children.length === 0) return;

        // 初始隐藏所有子节点
        children.forEach((child) => {
            child.active = false;
        });

        // 显示第一个节点并启动动画
        this.showNodeWithHalfScale(children, 0);
    }
    private showNodeWithHalfScale(children: Node[], index: number): void {
        if (index >= children.length) return;

        const child = children[index];
        child.active = true;
        child.setScale(new Vec3(0, 0, 1));

        // 前半部分动画：缩放到一半
        const halfScaleTween = tween(child)
            .to(0.03, { scale: new Vec3(0.5, 0.5, 1) }, { easing: 'quadOut' })
            .call(() => {
                // 缩放到一半时，触发下一个节点
                this.showNodeWithHalfScale(children, index + 1);
            })
            .start();

        // 后半部分动画：从一半缩放到完整
        const fullScaleTween = tween(child)
            .to(0.03, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
            .start();

        this.activeTweens.push(halfScaleTween, fullScaleTween);
    }

    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
        this.stopAllEffects(); // 停止所有动画
        // 可选：重置节点状态
        const children = this.nodeReward!.children;
        if (children) {
            children.forEach((child) => {
                child.active = false;
                child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放
            });
        }
    }


    private stopAllEffects(): void {
        // 停止所有 tween 动画
        this.activeTweens.forEach((tween) => {
            tween.stop();
        });
        this.activeTweens = [];

        // 清除所有 setTimeout
        this.activeTimeouts.forEach((timeoutId) => {
            clearTimeout(timeoutId as unknown as number);
        });
        this.activeTimeouts = [];
    }
}
