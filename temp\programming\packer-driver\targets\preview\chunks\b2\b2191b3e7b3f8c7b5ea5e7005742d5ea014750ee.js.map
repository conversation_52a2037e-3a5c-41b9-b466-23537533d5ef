{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventGroup.ts"], "names": ["WaveEventGroupContext", "WaveEventGroup", "WaveConditionFactory", "WaveActionFactory", "_decorator", "eWaveConditionType", "eWaveActionType", "Condition<PERSON><PERSON><PERSON>", "ccclass", "property", "type", "emitter", "bullet", "<PERSON><PERSON><PERSON>", "wave", "reset", "constructor", "ctx", "data", "context", "<PERSON><PERSON><PERSON><PERSON>", "actions", "buildConditionChain", "conditions", "map", "actData", "action", "create", "onLoad", "filter", "act", "canExecute", "evaluate", "chain", "for<PERSON>ach", "condData", "index", "condition", "push", "Spawn_Count", "Player_Level", "Spawn_Interval", "Spawn_Angle"], "mappings": ";;;gJASaA,qB,EAmBAC,c,EAsCPC,oB,EAeAC,iB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5EGC,MAAAA,U,OAAAA,U;;AALuDC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,e,iBAAAA,e;;AACvDC,MAAAA,c,iBAAAA,c;;;;;;;;;OAMvB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA8BN,U;;uCAEvBJ,qB,GAAN,MAAMA,qBAAN,CAA0D;AAAA;AAC7D;AAD6D,eAE7DW,OAF6D,GAE7C,IAF6C;AAG7D;AAH6D,eAI7DC,MAJ6D,GAI9C,IAJ8C;AAAA,eAK7DC,WAL6D,GAKzC,IALyC;AAAA,eAO7DC,IAP6D,GAO3C,IAP2C;AAAA;;AAS7DC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACA,eAAKC,WAAL,GAAmB,IAAnB;AACA,eAAKC,IAAL,GAAY,IAAZ;AACH;;AAd4D,O,GAiBjE;AACA;;;gCACab,c,GAAN,MAAMA,cAAN,CAAqB;AAOxBe,QAAAA,WAAW,CAACC,GAAD,EAA0BC,IAA1B,EAAoD;AAAA,eANtDA,IAMsD;AAAA,eAJ/DC,OAI+D;AAAA,eAH/DC,cAG+D;AAAA,eAF/DC,OAE+D;AAC3D,eAAKF,OAAL,GAAeF,GAAf;AACA,eAAKC,IAAL,GAAYA,IAAZ;AACA,eAAKE,cAAL,GAAsB,KAAKE,mBAAL,CAAyBJ,IAAI,CAACK,UAA9B,CAAtB;AACA,eAAKF,OAAL,GAAeH,IAAI,CAACG,OAAL,CAAaG,GAAb,CAAiBC,OAAO,IAAI;AACvC,gBAAMC,MAAM,GAAGvB,iBAAiB,CAACwB,MAAlB,CAAyBF,OAAzB,CAAf;;AACA,gBAAIC,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACE,MAAP,CAAc,KAAKT,OAAnB;AACA,qBAAOO,MAAP;AACH;;AACD,mBAAO,IAAP;AACH,WAPc,EAOZG,MAPY,CAOLC,GAAG,IAAIA,GAAG,KAAK,IAPV,CAAf;AAQH;;AAEOC,QAAAA,UAAU,GAAY;AAC1B,iBAAO,KAAKX,cAAL,CAAoBY,QAApB,CAA6B,KAAKb,OAAlC,CAAP;AACH;;AAEOG,QAAAA,mBAAmB,CAACC,UAAD,EAAkD;AACzE,cAAMU,KAAK,GAAG;AAAA;AAAA,iDAAd;AACAV,UAAAA,UAAU,CAACW,OAAX,CAAmB,CAACC,QAAD,EAAWC,KAAX,KAAqB;AACpC,gBAAMC,SAAS,GAAGnC,oBAAoB,CAACyB,MAArB,CAA4BQ,QAA5B,CAAlB;;AACA,gBAAIE,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACT,MAAV,CAAiB,KAAKT,OAAtB;AACAc,cAAAA,KAAK,CAACV,UAAN,CAAiBe,IAAjB,CAAsBD,SAAtB;AACH;AACJ,WAND;AAOA,iBAAOJ,KAAP;AACH;;AAnCuB,O;;AAsCtB/B,MAAAA,oB,GAAN,MAAMA,oBAAN,CAA2B;AACV,eAANyB,MAAM,CAACT,IAAD,EAAkD;AAC3D,kBAAQA,IAAI,CAACR,IAAb;AACI,iBAAK;AAAA;AAAA,0DAAmB6B,WAAxB;AACI,qBAAO,IAAP;;AACJ,iBAAK;AAAA;AAAA,0DAAmBC,YAAxB;AACI,qBAAO,IAAP;;AACJ;AACI;AANR;;AASA,iBAAO,IAAP;AACH;;AAZsB,O;AAerBrC,MAAAA,iB,GAAN,MAAMA,iBAAN,CAAwB;AACP,eAANwB,MAAM,CAACT,IAAD,EAA4C;AACrD,kBAAQA,IAAI,CAACR,IAAb;AACI,iBAAK;AAAA;AAAA,oDAAgB+B,cAArB;AACI,qBAAO,IAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBC,WAArB;AACI,qBAAO,IAAP;;AACJ;AACI;AANR;;AAQA,iBAAO,IAAP;AACH;;AAXmB,O", "sourcesContent": ["import { WaveConditionData, WaveEventGroupData, WaveActionData, eWaveConditionType, eWaveActionType } from \"../data/WaveData\";\r\nimport { IEventGroupContext, ConditionChain } from \"db://assets/bundles/common/script/game/bullet/EventGroup\";\r\nimport { IEventConditionData } from \"db://assets/bundles/common/script/game/data/bullet/EventGroupData\";\r\nimport { IEventCondition } from \"db://assets/bundles/common/script/game/bullet/conditions/IEventCondition\";\r\nimport { IEventAction } from \"db://assets/bundles/common/script/game/bullet/actions/IEventAction\";\r\nimport { _decorator, CCInteger } from \"cc\";\r\nimport { Wave } from \"./Wave\";\r\nconst { ccclass, property, type } = _decorator;\r\n\r\nexport class WaveEventGroupContext implements IEventGroupContext {\r\n    // 继承来的，在波次这里不使用\r\n    emitter: null = null;\r\n    // 继承来的，在波次这里不使用\r\n    bullet: null = null;\r\n    playerPlane: null = null;\r\n\r\n    wave: Wave|null = null;\r\n\r\n    reset() {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n        this.playerPlane = null;\r\n        this.wave = null;\r\n    }\r\n}\r\n\r\n/// Wave事件组\r\n/// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData\r\nexport class WaveEventGroup {\r\n    readonly data: WaveEventGroupData\r\n\r\n    context: IEventGroupContext;\r\n    conditionChain: ConditionChain;\r\n    actions: IEventAction[];\r\n    \r\n    constructor(ctx: IEventGroupContext, data: WaveEventGroupData) {\r\n        this.context = ctx;\r\n        this.data = data;\r\n        this.conditionChain = this.buildConditionChain(data.conditions);\r\n        this.actions = data.actions.map(actData => {\r\n            const action = WaveActionFactory.create(actData);\r\n            if (action) {\r\n                action.onLoad(this.context);\r\n                return action;\r\n            }\r\n            return null;\r\n        }).filter(act => act !== null) as IEventAction[];\r\n    }\r\n\r\n    private canExecute(): boolean {\r\n        return this.conditionChain.evaluate(this.context);\r\n    }\r\n\r\n    private buildConditionChain(conditions: WaveConditionData[]): ConditionChain {\r\n        const chain = new ConditionChain();\r\n        conditions.forEach((condData, index) => {\r\n            const condition = WaveConditionFactory.create(condData);\r\n            if (condition) {\r\n                condition.onLoad(this.context);\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n}\r\n\r\nclass WaveConditionFactory {\r\n    static create(data: WaveConditionData): IEventCondition | null {\r\n        switch (data.type) {\r\n            case eWaveConditionType.Spawn_Count:\r\n                return null;\r\n            case eWaveConditionType.Player_Level:\r\n                return null;\r\n            default: \r\n                break;\r\n        }\r\n        \r\n        return null;\r\n    }\r\n}\r\n\r\nclass WaveActionFactory {\r\n    static create(data: WaveActionData): IEventAction | null {\r\n        switch (data.type) {\r\n            case eWaveActionType.Spawn_Interval:\r\n                return null;\r\n            case eWaveActionType.Spawn_Angle:\r\n                return null;\r\n            default:\r\n                break;\r\n        }\r\n        return null;\r\n    }\r\n}"]}