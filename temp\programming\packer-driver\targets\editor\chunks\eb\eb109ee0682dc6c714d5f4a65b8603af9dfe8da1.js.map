{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsUI.ts"], "names": ["_decorator", "Node", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "List", "StatisticsScoreCell", "StatisticsHurtCell", "ccclass", "property", "StatisticsUI", "cells", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "btnNext", "onNextClick", "list", "numItems", "types", "idx", "cellsNode", "children", "for<PERSON>ach", "node", "cell", "getComponent", "setType", "push", "closeUI", "onShow", "onHide", "onClose", "onList<PERSON>ender", "listItem", "row"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AAEEC,MAAAA,mB,iBAAAA,mB;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;8BAIjBY,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACV,IAAD,C,2BAXb,MACaW,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAarCC,KAbqC,GAaP,EAbO;AAAA;;AAejB,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACA,eAAKC,OAAL,CAAcF,QAAd,CAAuB,KAAKG,WAA5B,EAAyC,IAAzC;AACA,eAAKC,IAAL,CAAWC,QAAX,GAAsB,EAAtB;AAEA,cAAIC,KAAe,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,CAAtB;AACA,cAAIC,GAAW,GAAG,CAAlB;AACA,eAAKC,SAAL,CAAgBC,QAAhB,CAAyBC,OAAzB,CAAiCC,IAAI,IAAI;AACrC,kBAAMC,IAAI,GAAGD,IAAI,CAACE,YAAL;AAAA;AAAA,yDAAb;AACAD,YAAAA,IAAI,QAAJ,IAAAA,IAAI,CAAEE,OAAN,CAAcR,KAAK,CAACC,GAAG,EAAJ,CAAnB;;AACA,gBAAIK,IAAI,KAAK,IAAb,EAAmB;AACf,mBAAKtB,KAAL,CAAWyB,IAAX,CAAgBH,IAAhB;AACH;AACJ,WAND;AAOH;;AACc,cAATX,SAAS,GAAG;AACd;AAAA;AAAA,8BAAMe,OAAN,CAAc3B,YAAd;AACH;;AACgB,cAAXc,WAAW,GAAG;AAChB;AAAA;AAAA,8BAAMa,OAAN,CAAc3B,YAAd;AACH;;AACW,cAAN4B,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAE9B;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,gBAAMV,IAAI,GAAGS,QAAQ,CAACR,YAAT;AAAA;AAAA,yDAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB,CAElB;AACJ;;AAxDoC,O;;;;;iBAGV,I;;;;;;;iBAEE,I;;;;;;;iBAGT,I;;;;;;;iBAGK,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport List from './components/list/List';\r\nimport { FriendCellUI } from '../friend/FriendCellUI';\r\nimport { StatisticsScoreCell } from './StatisticsScoreCell';\r\nimport { StatisticsHurtCell } from './StatisticsHurtCell';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('StatisticsUI')\r\nexport class StatisticsUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n\r\n    @property(List)\r\n    list: List | null = null;\r\n\r\n    @property(Node)\r\n    cellsNode: Node | null = null;\r\n\r\n    cells: StatisticsHurtCell[] = [];\r\n\r\n    public static getUrl(): string { return \"prefab/ui/StatisticsUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n        this.list!.numItems = 20;\r\n\r\n        let types: string[] = ['战机', '装备', '武器', '道具', '其他'];\r\n        let idx: number = 0;\r\n        this.cellsNode!.children.forEach(node => {\r\n            const cell = node.getComponent(StatisticsHurtCell);\r\n            cell?.setType(types[idx++]);\r\n            if (cell !== null) {\r\n                this.cells.push(cell);\r\n            }\r\n        });\r\n    }\r\n    async onOKClick() {\r\n        UIMgr.closeUI(StatisticsUI);\r\n    }\r\n    async onNextClick() {\r\n        UIMgr.closeUI(StatisticsUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {\r\n        const cell = listItem.getComponent(StatisticsScoreCell);\r\n        if (cell !== null) {\r\n\r\n        }\r\n    }\r\n}\r\n"]}