
import { eTargetValueType, eWrapMode, IEventActionData } from "../../data/bullet/EventGroupData";
import { IEventGroupContext, EventGroupContext } from "../EventGroup";
import { Easing} from "../Easing";
import { EDITOR } from "cc/env";

export interface IEventAction {
    readonly data: IEventActionData;

    isCompleted(): boolean;

    onLoad(context: IEventGroupContext): void;
    onExecute(context: IEventGroupContext, dt: number): void;

    // onCancel? onComplete?
}

export class EventActionBase implements IEventAction {
    readonly data: IEventActionData;

    protected _isCompleted: boolean = false;
    protected _elapsedTime: number = 0;
    protected _startValue: number = 0;
    protected _targetValue: number = 0;
    protected _delay: number = 0;
    // 这里有两个时间：
    // _transitionDuration是从_startValue->_targetValue所需要的时间
    // _duration是整个action执行的生命周期
    protected _transitionDuration: number = 0;
    protected _duration: number = 0;

    constructor(data: IEventActionData) {
        this.data = data;
    }

    isCompleted(): boolean {
        return this._isCompleted;
    }

    canLerp(): boolean {
        return true;
    }

    onLoad(context: IEventGroupContext): void {
        this._isCompleted = false;
        this._elapsedTime = 0;
        this._delay = this.data.delay.eval();
        this._duration = this.data.duration.eval();
        this._transitionDuration = this.data.transitionDuration.eval();
        this.resetStartValue(context);
        this.resetTargetValue(context);

        if (EDITOR) {
            console.log(`EventAction:onLoad: ${this.data.name}`);
        }
    }

    onExecute(context: IEventGroupContext, dt: number): void {
        this._elapsedTime += dt;

        if (this._elapsedTime < this._delay) {
            return;
        }

        if (this._elapsedTime >= this._duration) {
            this.onExecuteInternal(context, this._targetValue);
            this._isCompleted = true;
        }
        else if (this.canLerp()) {
            this.onExecuteInternal(context, this.lerpValue(this._startValue, this._targetValue));
        }
    }

    lerpValue(startValue: number, targetValue: number): number {
        if (this._transitionDuration <= 0) {
            return targetValue;
        }

        let progress = this._elapsedTime / this._transitionDuration;
        // Handle wrap modes when transition duration is exceeded
        if (progress > 1.0) {
            switch (this.data.wrapMode) {
                case eWrapMode.Once:
                    progress = 1.0;
                    break;
                case eWrapMode.Loop:
                    progress = progress % 1.0;
                    break;
                case eWrapMode.Pingpong:
                    const cycle = Math.floor(progress);
                    const localProgress = progress % 1.0;
                    progress = (cycle % 2 === 0) ? localProgress : (1.0 - localProgress);
                    break;
            }
        }

        return Easing.lerp(this.data.easing, startValue, targetValue, progress);
    }

    // override this to get the correct start value
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = 0;
    }

    protected resetTargetValue(context: IEventGroupContext): void {
        switch (this.data.targetValueType)
        {
            case eTargetValueType.Relative:
                this._targetValue = this.data.targetValue.eval() + this._startValue;
                break;
            default:
                this._targetValue = this.data.targetValue.eval();
                break;
        }
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        // Default implementation does nothing
    }
}
