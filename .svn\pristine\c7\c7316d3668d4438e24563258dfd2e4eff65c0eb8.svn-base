[{"__type__": "cc.SceneAsset", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "LevelEditor", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 353}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 354}, "_id": "401efd7e-bd20-4537-a13a-f25e6238c2a9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 74}, {"__id__": 252}, {"__id__": 266}, {"__id__": 267}, {"__id__": 282}, {"__id__": 289}], "_active": true, "_components": [{"__id__": 292}, {"__id__": 293}, {"__id__": 294}, {"__id__": 295}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 666.9999999999999, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 73}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46ihxbAfVPT4CTuCqzBBFA"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "_active": true, "_components": [{"__id__": 72}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1136.7999999999952, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a4JVJXICJI26tX1RZCzZEA"}, {"__type__": "cc.Node", "_name": "backgrounds", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 38}, {"__id__": 43}, {"__id__": 48}, {"__id__": 53}, {"__id__": 58}, {"__id__": 63}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a1tYSw/ThOSoAm49Vn1r4o"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f3HDx8BYFGdKKSZa2IDEvN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 11}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -66.99999999999989, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 15}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "85la0dtbJMGqeSphAG58VQ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 1133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 19}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 20}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "afEMdW/HlGR5xEztDsopyv", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 21}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2333, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 25}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8c8x7Np3ZCMLs9zVj0pY3h", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 26}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 3533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 29}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 28}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 30}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d22CAUBulL/oHjiTSVOsAf", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 4733, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 34}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 35}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "99+h2nT4hIz4DqpOfRk3Me", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 5933, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 39}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 38}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 40}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "19b1SWPUJDoLMAgZN5rvLr", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 41}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 7133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 44}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 43}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 45}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eaqpp/glNGTouzr5n4+rQT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 46}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 8333, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 49}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 48}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 50}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "475A/b9ghDipHF1aYcpqbS", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 51}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 9533, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 54}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 53}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 55}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c3ER0ITAhHYo17bvs770qp", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 56}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 10733, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 59}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 60}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "30DdGX9tJNVKjvdQdRT1QT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 61}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 11933, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 64}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 63}, "asset": {"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 65}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7bXZve6ppKqY1P4GHFnnfL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 66}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 13133, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ba0wTpY0dCzYbhGXETmRRA"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "47t+8s1NhCQa9n2ZD8gWU1"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8dyDB6wctD0IoX3LxU1qXj"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49TGa9uxNKtJcndFW243l3"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_id": "37GH/dcqVEH6kaVwhxQNKq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c7cr3UHz9ID5OL86RVgF3A"}, {"__type__": "cc.Node", "_name": "FloorLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 75}, {"__id__": 81}, {"__id__": 99}, {"__id__": 185}, {"__id__": 203}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62oMpiqNVJeLfnCzN4mnAI"}, {"__type__": "cc.Node", "_name": "layer_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_active": true, "_components": [{"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1136.7999999999952, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "70d6mQbZZCobIKJP/sxoxv"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ad7dvKi99DCpeSFtL4fDDN"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7ev5Nr/B5AtpJn0GReGkF2"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5aRU6epGtO0LDVYpLq3nS5"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89nDehypFPd5vaamVuh5jE"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": null, "_id": "efWsXCsgxKTLX+SVszyHlg"}, {"__type__": "cc.Node", "_name": "layer_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [{"__id__": 82}, {"__id__": 83}, {"__id__": 84}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1136.7999999999952, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "28tPxLQtdFCYsmTPy/kewy"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46gbY5TDNKYqY0f4g4mi7z"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27AGnAYK9NjLznM93lwCqT"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [{"__id__": 85}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0ep7jj2KJFvLm/U3cOTEaT"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cdLXE9jbJKerf6nbuOC2+9"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 85}, "_prefab": {"__id__": 87}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 86}, "asset": {"__uuid__": "4819c2df-3844-4b2f-9a27-6496232220f5", "__expectedType__": "cc.Prefab"}, "fileId": "e6gsbpksNFNKSa6hJ0q0NX", "instance": {"__id__": 88}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2dOHh8oaJK9LigtUKZw8Bu", "prefabRootNode": null, "mountedChildren": [{"__id__": 89}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 90}, "nodes": [{"__id__": 91}, {"__id__": 94}]}, {"__type__": "cc.TargetInfo", "localID": ["e6gsbpksNFNKSa6hJ0q0NX"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 86}, "_prefab": {"__id__": 92}, "__editorExtras__": {"mountedRoot": {"__id__": 86}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 91}, "asset": {"__uuid__": "df3ecd42-2e9e-4cdd-9e15-96b95bc775b8", "__expectedType__": "cc.Prefab"}, "fileId": "fdho6neONMQpTJ/VskKEES", "instance": {"__id__": 93}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "eeB6jK7WhHc4xAUXVOsy8K", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 86}, "_prefab": {"__id__": 95}, "__editorExtras__": {"mountedRoot": {"__id__": 86}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 94}, "asset": {"__uuid__": "b0e825f0-cdc7-45d9-b5ba-15e1f24b6b59", "__expectedType__": "cc.Prefab"}, "fileId": "19EMmrCeJPeLzXcAtn+Zev", "instance": {"__id__": 96}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "46lBdzjvJEc7IMEqYvWTro", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "957FFxSBdJD7gNfXoucvSB"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_id": "eeF/3INVNFwazJ4SpgENYD"}, {"__type__": "cc.Node", "_name": "layer_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [{"__id__": 100}, {"__id__": 101}, {"__id__": 182}, {"__id__": 183}], "_active": true, "_components": [{"__id__": 184}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1364.1599999999944, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2bswmJwjVIF6zUaew0LkbR"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "93+29KB4ZGQJzPNJNswGfw"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [{"__id__": 102}, {"__id__": 124}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "76rII82VlFQZMCC3iHaun+"}, {"__type__": "cc.Node", "_name": "scroll_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}, {"__id__": 106}, {"__id__": 109}, {"__id__": 112}, {"__id__": 115}, {"__id__": 118}, {"__id__": 121}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56YHbPt9pAuoHvOCUfZ4gd"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 104}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 103}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 105}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e36GMvITNKaZUMctaBHY9f", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 107}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 106}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 108}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "02nvUODMtN+L26CcL8IYyg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 110}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 111}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "d40ciyWOhJ345ZhwutR2yu", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 113}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 112}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 114}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "49P4JvJWlFOp/ksf5Fufz7", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 116}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 117}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3cYGfHTFlJJ7Ev3kXOk/MQ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 119}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 118}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 120}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a0CvrpOatO9oJSHIExIeTc", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 102}, "_prefab": {"__id__": 122}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 121}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 123}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e9nkPC87dFPIjOHUlDvLEH", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "scroll_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [{"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 134}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}, {"__id__": 149}, {"__id__": 152}, {"__id__": 155}, {"__id__": 158}, {"__id__": 161}, {"__id__": 164}, {"__id__": 167}, {"__id__": 170}, {"__id__": 173}, {"__id__": 176}, {"__id__": 179}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "91V8vtfHZOnYR9tfScmclB"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 126}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 125}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 127}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c4F9uaqDpKa4x9w1BAKY2O", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 129}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 130}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "07gaYESNZKTonHwZ1leZeK", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 132}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 131}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 133}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9eMkdy1iRAnpIMoXB7EvH2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 135}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 136}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b0/q2iEFZLeKTBY7Wgu7x3", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 138}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 139}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "23YC358q1GAKIEvD4pYEIi", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 141}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 140}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 142}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "55+1dqJ2JO7L0bjrvmbHdJ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 144}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 143}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 145}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0fp0zV28pO17Z1JdKvUpqb", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 147}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 146}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 148}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9cE+p/HyVCHL5AMMk4sRbS", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 150}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 149}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 151}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "65TlC0ye9HMKXffALqHGkH", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 153}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 152}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 154}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "72DgbJ0CNCuoiV+ASiA8nl", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 156}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 155}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 157}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "2f+/T+bFZFSJvMza34St0v", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 159}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 158}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 160}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "dd0kP/bLZJwK2A+wyDQSe9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 162}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 163}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "e90c/qIN9NB6G4Ze3zyKFy", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 165}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 164}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 166}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1e0gpTUW9JlYyzfUJCCTok", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 168}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 167}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 169}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "1bMIzCNSxF7JV9VQxB+4qE", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 171}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 170}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 172}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7eSJXPYX1CT4PN2As8dhOZ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 174}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 173}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 175}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "baJ05L8ZtJea+bfHUepGIg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 177}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 176}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 178}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "cfvvMjndNGcqneZOkAG5WZ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 124}, "_prefab": {"__id__": 180}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 179}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 181}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b6V3DU3YJCP4OUNswtiepi", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fjnV2+w9Eu551K8/Uam3L"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11N4CM2V5CjJ3kKDyMKhP4"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_id": "82YJk1jz9DvLzuBaZ9zdKI"}, {"__type__": "cc.Node", "_name": "layer_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [{"__id__": 186}, {"__id__": 187}, {"__id__": 188}, {"__id__": 201}], "_active": true, "_components": [{"__id__": 202}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1193.639999999995, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c26mtVxs9CKqrgORW5go73"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 185}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8b4RA4ZgNFyJzLx3UVlPyA"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 185}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5gWLYz2pCLYvqGtKb/iT3"}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 185}, "_children": [{"__id__": 189}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9de2l2NBtFBJ0n8bPE7oZU"}, {"__type__": "cc.Node", "_name": "dyna_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 188}, "_children": [{"__id__": 190}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "38aSiLDFlOHLzo5kVvCuqI"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 189}, "_prefab": {"__id__": 191}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 190}, "asset": {"__uuid__": "dc1d26a4-7614-469b-b7c9-f6329f1cc451", "__expectedType__": "cc.Prefab"}, "fileId": "56xTtHP4tJg4s0D/yyljUz", "instance": {"__id__": 192}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "54RmqbrKtJpo9EfC9aBaZs", "prefabRootNode": null, "mountedChildren": [{"__id__": 193}], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 194}, "nodes": [{"__id__": 195}, {"__id__": 198}]}, {"__type__": "cc.TargetInfo", "localID": ["56xTtHP4tJg4s0D/yyljUz"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 190}, "_prefab": {"__id__": 196}, "__editorExtras__": {"mountedRoot": {"__id__": 190}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 195}, "asset": {"__uuid__": "1f86a690-2793-4143-a94d-c4aa0099b0da", "__expectedType__": "cc.Prefab"}, "fileId": "ebE3ClO65FEavwhVAL65ht", "instance": {"__id__": 197}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e8d5xF7eNKRZvGz5p1Jixg", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 190}, "_prefab": {"__id__": 199}, "__editorExtras__": {"mountedRoot": {"__id__": 190}}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 198}, "asset": {"__uuid__": "b70143d5-a8b5-4c0f-8f7a-4c320af9b7d3", "__expectedType__": "cc.Prefab"}, "fileId": "87wMLDdGJGwoHG0lk0VpLH", "instance": {"__id__": 200}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a4ZJHcFxVKhrCs/ogbpcrj", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 185}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "984Cl2ZsBFcacwRvV+fmf1"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 185}, "_enabled": true, "__prefab": null, "_id": "047VrbgWBHc71FnzIPYe9C"}, {"__type__": "cc.Node", "_name": "layer_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [{"__id__": 204}, {"__id__": 205}, {"__id__": 249}, {"__id__": 250}], "_active": true, "_components": [{"__id__": 251}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1705.1999999999928, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fhBsOcJZNaJj4VI20gx+q"}, {"__type__": "cc.Node", "_name": "terrains", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6fBEcQBiVGNab64O81d7Ag"}, {"__type__": "cc.Node", "_name": "scrolls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [{"__id__": 206}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "efs44WmaxIgbGQAdSgTEo/"}, {"__type__": "cc.Node", "_name": "scroll_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 205}, "_children": [{"__id__": 207}, {"__id__": 210}, {"__id__": 213}, {"__id__": 216}, {"__id__": 219}, {"__id__": 222}, {"__id__": 225}, {"__id__": 228}, {"__id__": 231}, {"__id__": 234}, {"__id__": 237}, {"__id__": 240}, {"__id__": 243}, {"__id__": 246}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f0jnfARPxA8LclIiGx0/WK"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 208}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 207}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 209}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b3dAnC+XNCYqIM3aH2tKtT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 211}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 212}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "adQZf8+FNCJJqNSR3YuFAk", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 214}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 213}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 215}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6akkLcFXRMP6v8qWVy8V09", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 217}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 216}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 218}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b1Kads3FNJ3b+KMG3Ag9uy", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 220}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 219}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 221}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4f6+YlK9FHQJTMqn4CecY8", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 223}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 222}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 224}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "72UvzU8NhK+Y7Ol5o/ThwW", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 226}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 225}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 227}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "aac9CS9dxIqrUhre464Nb9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 229}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 230}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "33ZBHJ3bZLYbiIMW5OtV52", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 232}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 231}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 233}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c4bTxxgflEB4m72OlPpEdb", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 235}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 234}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 236}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "72beC91+BH86w9qLPjDBa+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 238}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 237}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 239}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "61ntY+0RZKuolrhRUeT36a", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 241}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 240}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 242}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "80t55LHYNHNbnf7t40l56R", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 244}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 243}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 245}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "80CLTI9YxJebeJgwSHB3JV", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 206}, "_prefab": {"__id__": 247}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 246}, "asset": {"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}, "fileId": "8fYoFhq4xNa73NSIbcJTxF", "instance": {"__id__": 248}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c5oajTrCJFnr3AN1kdGVuw", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_name": "dynamic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63qFU03cZFHKeLyFlmWmcz"}, {"__type__": "cc.Node", "_name": "events", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "25TYZcCD5PgbAuf8k5Ewrm"}, {"__type__": "92d5epe+XRMm6NUwiCSJBKR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 203}, "_enabled": true, "__prefab": null, "_id": "3bSXBRIIBDB7lFNClgi6DZ"}, {"__type__": "cc.Node", "_name": "MainPlane", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 253}, {"__id__": 255}, {"__id__": 260}], "_active": true, "_components": [{"__id__": 265}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5Llji/3pG8ZUsk+/AnCts"}, {"__type__": "cc.Node", "_name": "enemy", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 252}, "_children": [], "_active": true, "_components": [{"__id__": 254}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22ge7J4Q5BcrDOrtqtJWcy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0e4ol/pP9AdL9y81NxK1IW"}, {"__type__": "cc.Node", "_name": "Plane 128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 252}, "_children": [{"__id__": 256}], "_active": true, "_components": [{"__id__": 259}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbYdiDAGtJNILT2mAHeQHg"}, {"__type__": "cc.Node", "_name": "128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -146.727, "y": -381.491, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1innlyJpPfbCEAHtzu7nR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 126, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcvUM8lEVG7r+Duly9xTUi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bcbad599-6805-464f-b852-c6330a6cc136@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b9z9YmlO1ORJddzAvkDa26"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "69CWNsBiBF8qLjUsjYBXVD"}, {"__type__": "cc.Node", "_name": "Plane 150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 252}, "_children": [{"__id__": 261}], "_active": true, "_components": [{"__id__": 264}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fSyYswbxJMYFuKxLm+oIa"}, {"__type__": "cc.Node", "_name": "150", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 260}, "_children": [], "_active": true, "_components": [{"__id__": 262}, {"__id__": 263}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 49.887, "y": -384.426, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97bvio7axI36wm/98rDd6y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4c8v2GIyBL7p+TWWFkd8E0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "13a76ed5-7bc0-444c-b47f-8beab0558280@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c3kMW+vo1DD4E5mvwzvbme"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dfuqEV4hA5JIX88WPdF7X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 252}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dVZOPnxBB55oxOGHxThGs"}, {"__type__": "cc.Node", "_name": "SkyLayers", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3kr0ELyxNI6WeWVx+sGNb"}, {"__type__": "cc.Node", "_name": "DrawNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 268}, {"__id__": 271}, {"__id__": 274}, {"__id__": 277}], "_active": true, "_components": [{"__id__": 280}, {"__id__": 281}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33t5ymWlpLkbT5idbqGQ7X"}, {"__type__": "cc.Node", "_name": "draw<PERSON>iew", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 270}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65LbppaIRGz7EVQ8Ktul+X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1Cf74eydN0Yy5qbbxInaR"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "75zN+z8lVGqLYHx2jxa0qd"}, {"__type__": "cc.Node", "_name": "drawMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9btiDQ+VZN95aFsLmdMyGC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9eg7PH/u9Il7l5kqhTVfqD"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "f7nWk9GTBEfo/KF0FlisGD"}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": false, "_components": [{"__id__": 275}, {"__id__": 276}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 621.287, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03lbeQRQBBaJmCmQbmnIP1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "0aPFKUoHhJCY99iaVm3K/C"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "时间：5.68", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d7uTBlts9OhJ188cabfoNE"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": false, "_components": [{"__id__": 278}, {"__id__": 279}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 458.235, "y": 454.781, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cW5DvaP9E7bNc04cxSN9/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "d5aSmlRnRDlJwqRXq14CD4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "进度：0.08", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 61, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "038VoLC5NCxYX7kf/kDiDH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cewjtt/5IRbxIivL8bKq5"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 10, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_miterLimit": 10, "_id": "23xs7GFb1G6bRSeyAo5w9K"}, {"__type__": "cc.Node", "_name": "GizmoManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 283}], "_active": true, "_components": [{"__id__": 286}, {"__id__": 287}, {"__id__": 288}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "23TQ+5yTNAyK7H6psLGXWg"}, {"__type__": "cc.Node", "_name": "Icon_trigger.png", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [], "_active": true, "_components": [{"__id__": 284}, {"__id__": 285}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 119.21299999999997, "y": 11.513000000001284, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "096A9BMyNJNYy5JmDfnS8X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "09sXl/MflM9q53kz230/qS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "abSJuL7iJNrrhE+lrrTQYH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9jCsZET9L+Ybd8UX16F6R"}, {"__type__": "35b7e0iBnFHtqqvAd1SurM7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": null, "gizmosEnabled": true, "drawInPlayMode": false, "refreshRate": 60, "maxDrawDistance": 2000, "_id": "c15PFCb7JLcrLysr6F+DtZ"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "85LkKkzdVB45KD5PBufbQj"}, {"__type__": "cc.Node", "_name": "WavePreview", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 290}, {"__id__": 291}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97hK/8rTxLfrLJQXLgQpZi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "21ZgnNk/FGEYxocAGfy5xD"}, {"__type__": "e6727UuHORMv49xH6m7Wz2U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_id": "96jNiemDdM35UIwxyvgTsh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "68a25Vb5mhGApMaV59XFjQ0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "progress": 0.08119999999999966, "_id": "61Fg6c5BVE0I4TKZPyCZ+2"}, {"__type__": "a4bf2J2KGJJV7RbX1jwoQWJ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "levelname": "高空-海洋风格", "totalTime": 70, "backgroundLayer": {"__id__": 296}, "floorLayers": [{"__id__": 303}, {"__id__": 310}, {"__id__": 320}, {"__id__": 333}, {"__id__": 343}], "skyLayers": [], "_id": "f2rN/MimJBS6HE7SikeuYU"}, {"__type__": "LevelEditorBackgroundLayer", "remark": "海面背景", "zIndex": 0, "node": {"__id__": 6}, "speed": 200, "scrollLayers": [], "randomLayers": [], "emittierLayer": {"__id__": 297}, "backgrounds": [{"__uuid__": "4c4ae1d7-4d0f-420e-bffc-195807ab0c8d", "__expectedType__": "cc.Prefab"}]}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 298}, "interval": 0, "intervalModify": {"__id__": 299}, "angle": 0, "angleModify": {"__id__": 300}, "speed": 0, "speedModify": {"__id__": 301}, "offSetX": {"__id__": 302}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "地形物件", "zIndex": 0, "node": {"__id__": 75}, "speed": 200, "scrollLayers": [], "randomLayers": [], "emittierLayer": {"__id__": 304}}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 305}, "interval": 0, "intervalModify": {"__id__": 306}, "angle": 0, "angleModify": {"__id__": 307}, "speed": 0, "speedModify": {"__id__": 308}, "offSetX": {"__id__": 309}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "随机地形物件组", "zIndex": 1, "node": {"__id__": 81}, "speed": 200, "scrollLayers": [], "randomLayers": [{"__id__": 311}], "emittierLayer": {"__id__": 314}}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 312}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": [{"__id__": 313}]}, {"__type__": "LevelEditorRandTerrainUI", "weight": 100, "terrainElement": {"__uuid__": "4819c2df-3844-4b2f-9a27-6496232220f5", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 315}, "interval": 0, "intervalModify": {"__id__": 316}, "angle": 0, "angleModify": {"__id__": 317}, "speed": 0, "speedModify": {"__id__": 318}, "offSetX": {"__id__": 319}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "雾气", "zIndex": 3, "node": {"__id__": 99}, "speed": 240, "scrollLayers": [{"__id__": 321}, {"__id__": 324}], "randomLayers": [], "emittierLayer": {"__id__": 327}}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefabs": [{"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}], "weight": 40, "splicingMode": 3, "splicingOffsetX": {"__id__": 322}, "splicingOffsetY": {"__id__": 323}}, {"__type__": "LayerEditorRandomRange", "min": -220, "max": 220}, {"__type__": "LayerEditorRandomRange", "min": -300, "max": 300}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefabs": [{"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}], "weight": 60, "splicingMode": 3, "splicingOffsetX": {"__id__": 325}, "splicingOffsetY": {"__id__": 326}}, {"__type__": "LayerEditorRandomRange", "min": -300, "max": 300}, {"__type__": "LayerEditorRandomRange", "min": -400, "max": 400}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 328}, "interval": 0, "intervalModify": {"__id__": 329}, "angle": 0, "angleModify": {"__id__": 330}, "speed": 0, "speedModify": {"__id__": 331}, "offSetX": {"__id__": 332}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "随机开场云", "zIndex": 4, "node": {"__id__": 185}, "speed": 210, "scrollLayers": [], "randomLayers": [{"__id__": 334}], "emittierLayer": {"__id__": 337}}, {"__type__": "LevelEditorRandTerrainsLayersUI", "dynamicTerrains": [{"__id__": 335}]}, {"__type__": "LevelEditorRandTerrainsLayerUI", "weight": 100, "dynamicTerrain": [{"__id__": 336}]}, {"__type__": "LevelEditorRandTerrainUI", "weight": 100, "terrainElement": {"__uuid__": "dc1d26a4-7614-469b-b7c9-f6329f1cc451", "__expectedType__": "cc.Prefab"}}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 338}, "interval": 0, "intervalModify": {"__id__": 339}, "angle": 0, "angleModify": {"__id__": 340}, "speed": 0, "speedModify": {"__id__": 341}, "offSetX": {"__id__": 342}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorLayer", "remark": "滤镜遮罩层", "zIndex": 5, "node": {"__id__": 203}, "speed": 300, "scrollLayers": [{"__id__": 344}], "randomLayers": [], "emittierLayer": {"__id__": 347}}, {"__type__": "LevelEditorScrollLayerUI", "scrollPrefabs": [{"__uuid__": "b9ef1436-a099-42a4-82ef-f72e8ceffafa", "__expectedType__": "cc.Prefab"}], "weight": 100, "splicingMode": 1, "splicingOffsetX": {"__id__": 345}, "splicingOffsetY": {"__id__": 346}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LevelEditorEmittierLayerUI", "bStart": false, "emittier": null, "type": 1, "value": 0, "initDelay": 0, "delayModify": {"__id__": 348}, "interval": 0, "intervalModify": {"__id__": 349}, "angle": 0, "angleModify": {"__id__": 350}, "speed": 0, "speedModify": {"__id__": 351}, "offSetX": {"__id__": 352}}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "LayerEditorRandomRange", "min": 0, "max": 0}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "401efd7e-bd20-4537-a13a-f25e6238c2a9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 8}, {"__id__": 13}, {"__id__": 18}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 38}, {"__id__": 43}, {"__id__": 48}, {"__id__": 53}, {"__id__": 58}, {"__id__": 63}, {"__id__": 86}, {"__id__": 91}, {"__id__": 94}, {"__id__": 103}, {"__id__": 106}, {"__id__": 109}, {"__id__": 112}, {"__id__": 115}, {"__id__": 118}, {"__id__": 121}, {"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 134}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}, {"__id__": 149}, {"__id__": 152}, {"__id__": 155}, {"__id__": 158}, {"__id__": 161}, {"__id__": 164}, {"__id__": 167}, {"__id__": 170}, {"__id__": 173}, {"__id__": 176}, {"__id__": 179}, {"__id__": 190}, {"__id__": 195}, {"__id__": 198}, {"__id__": 207}, {"__id__": 210}, {"__id__": 213}, {"__id__": 216}, {"__id__": 219}, {"__id__": 222}, {"__id__": 225}, {"__id__": 228}, {"__id__": 231}, {"__id__": 234}, {"__id__": 237}, {"__id__": 240}, {"__id__": 243}, {"__id__": 246}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 355}, "shadows": {"__id__": 356}, "_skybox": {"__id__": 357}, "fog": {"__id__": 358}, "octree": {"__id__": 359}, "skin": {"__id__": 360}, "lightProbeInfo": {"__id__": 361}, "postSettings": {"__id__": 362}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]