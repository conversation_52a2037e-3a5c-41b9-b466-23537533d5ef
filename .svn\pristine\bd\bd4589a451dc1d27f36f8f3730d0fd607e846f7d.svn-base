import { WaveConditionData, WaveEventGroupData, WaveActionData, eWaveConditionType, eWaveActionType } from "../data/WaveData";
import { IEventGroupContext, ConditionChain } from "db://assets/bundles/common/script/game/bullet/EventGroup";
import { IEventConditionData } from "db://assets/bundles/common/script/game/data/bullet/EventGroupData";
import { IEventCondition } from "db://assets/bundles/common/script/game/bullet/conditions/IEventCondition";
import { IEventAction } from "db://assets/bundles/common/script/game/bullet/actions/IEventAction";
import { _decorator, CCInteger } from "cc";
import { Wave } from "./Wave";
const { ccclass, property, type } = _decorator;

export class WaveEventGroupContext implements IEventGroupContext {
    // 继承来的，在波次这里不使用
    emitter: null = null;
    // 继承来的，在波次这里不使用
    bullet: null = null;
    playerPlane: null = null;

    wave: Wave|null = null;

    reset() {
        this.emitter = null;
        this.bullet = null;
        this.playerPlane = null;
        this.wave = null;
    }
}

/// Wave事件组
/// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData
export class WaveEventGroup {
    readonly data: WaveEventGroupData

    context: IEventGroupContext;
    conditionChain: ConditionChain;
    actions: IEventAction[];
    
    constructor(ctx: IEventGroupContext, data: WaveEventGroupData) {
        this.context = ctx;
        this.data = data;
        this.conditionChain = this.buildConditionChain(data.conditions);
        this.actions = data.actions.map(actData => {
            const action = WaveActionFactory.create(actData);
            if (action) {
                action.onLoad(this.context);
                return action;
            }
            return null;
        }).filter(act => act !== null) as IEventAction[];
    }

    private canExecute(): boolean {
        return this.conditionChain.evaluate(this.context);
    }

    private buildConditionChain(conditions: WaveConditionData[]): ConditionChain {
        const chain = new ConditionChain();
        conditions.forEach((condData, index) => {
            const condition = WaveConditionFactory.create(condData);
            if (condition) {
                condition.onLoad(this.context);
                chain.conditions.push(condition);
            }
        });
        return chain;
    }
}

class WaveConditionFactory {
    static create(data: WaveConditionData): IEventCondition | null {
        switch (data.type) {
            case eWaveConditionType.Spawn_Count:
                return null;
            case eWaveConditionType.Player_Level:
                return null;
            default: 
                break;
        }
        
        return null;
    }
}

class WaveActionFactory {
    static create(data: WaveActionData): IEventAction | null {
        switch (data.type) {
            case eWaveActionType.Spawn_Interval:
                return null;
            case eWaveActionType.Spawn_Angle:
                return null;
            default:
                break;
        }
        return null;
    }
}