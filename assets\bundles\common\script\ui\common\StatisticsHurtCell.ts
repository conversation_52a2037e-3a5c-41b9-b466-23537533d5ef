import { _decorator, Component, Label, ProgressBar, Sprite, tween } from 'cc';
import { MessageBox } from 'db://assets/scripts/core/base/MessageBox';
const { ccclass, property } = _decorator;

@ccclass('StatisticsHurtCell')
export class StatisticsHurtCell extends Component {

    @property(Sprite)
    icon: Sprite | null = null;
    @property(Label)
    txtType: Label | null = null;
    @property(ProgressBar)
    bar: ProgressBar | null = null;
    @property(Label)
    txt: Label | null = null;
    @property(Label)
    txt2: Label | null = null;

    private _expTween: any;   // 经验条动画的 tween 引用

    start() {
        const gap = 0.5;
        const exp = 0.8;
        const finalExp = 10000; // 最终值
        this._expTween = tween(this.bar!)
            .to(gap, { progress: exp }, {
                onUpdate: () => {
                    if (!this.bar) return;
                    this.txt!.string = `${Math.round(finalExp * this.bar.progress)}/${finalExp}`;
                }
            })
            .start();
    }

    update(deltaTime: number) {

    }

    onDestroy() {
        if (this._expTween) this._expTween.stop();
    }

    public setType(type: string) {
        this.txtType!.string = type;
        MessageBox.show(this.txtType!.string);
    }
}


