import { error } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import { ResEnemy } from "../../autogen/luban/schema";

export class EnemyData extends AttributeData {
    _planeId: number = 0;//飞机id

    config: ResEnemy | null = null;//飞机静态配置

    get planeId() {
        return this._planeId;
    }

    set planeId(value) {
        if (value != this._planeId) {
            this._planeId = value;
            this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;
            this.updateData();
        }
    }

    get recourseSpine() {
        if (!this.config) {
            return "";
        }
    }

    updateData() {
        if (!this.config) {
            error(`enemyPlane ${this._planeId}: config is null, cannot update attributes.`);
            return;
        }
        this.setBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.baseHp);
        this.setBaseAttribute(AttributeConst.AttackOutAdd, this.config.baseAtk);
    }
}