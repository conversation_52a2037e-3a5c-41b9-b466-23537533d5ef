{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts"], "names": ["EventGroupComp", "BaseComp", "plane", "init", "entity"], "mappings": ";;;wCAIaA,c;;;;;;;;;;;;;;;;;;;;;;AAHNC,MAAAA,Q;;;;;;;gCAGMD,c,GAAN,MAAMA,cAAN;AAAA;AAAA,gCAAsC;AAAA;AAAA;AAAA,eACzCE,KADyC,GACZ,IADY;AAAA;;AAGzCC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,gBAAMD,IAAN,CAAWC,MAAX;AAEA,eAAKF,KAAL,GAAaE,MAAb;AACH;;AAPwC,O", "sourcesContent": ["import Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\nimport BaseComp from \"db://assets/bundles/common/script/game/ui/base/BaseComp\";\r\nimport EnemyPlaneBase from \"db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase\";\r\n\r\nexport class EventGroupComp extends BaseComp {\r\n    plane: EnemyPlaneBase|null = null;\r\n\r\n    init(entity: Entity) {\r\n        super.init(entity);\r\n\r\n        this.plane = entity as EnemyPlaneBase;\r\n    }\r\n}"]}