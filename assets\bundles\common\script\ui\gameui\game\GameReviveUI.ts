import { _decorator, Node } from 'cc';

import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { GameIns } from '../../../game/GameIns';

const { ccclass, property } = _decorator;

@ccclass('GameReviveUI')
export class GameReviveUI extends BaseUI {

    public static getUrl(): string { return "ui/game/GameReviveUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }

    protected onLoad(): void {

    }
    async closeUI() {
        UIMgr.closeUI(GameReviveUI);
    }
    async onShow(): Promise<void> {
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }

    onBtnReviveClicked() {
        GameIns.battleManager.relifeBattle();
        this.closeUI();
    }

    onBtnAdClicked() {
        this.closeUI();
        GameIns.battleManager.relifeBattle();
    }
}


