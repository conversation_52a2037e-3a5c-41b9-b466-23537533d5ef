System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventActionBase, WaveActionBase, WaveAction_SpawnInterval, WaveAction_SpawnAngle, WaveAction_PlaneSpeed, _crd;

  function _reportPossibleCrUseOfEventActionBase(extras) {
    _reporterNs.report("EventActionBase", "db://assets/bundles/common/script/game/bullet/actions/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "db://assets/bundles/common/script/game/bullet/EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroupContext(extras) {
    _reporterNs.report("WaveEventGroupContext", "./WaveEventGroup", _context.meta, extras);
  }

  _export({
    WaveActionBase: void 0,
    WaveAction_SpawnInterval: void 0,
    WaveAction_SpawnAngle: void 0,
    WaveAction_PlaneSpeed: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      EventActionBase = _unresolved_2.EventActionBase;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0d399l1L+dCYaAROpB5hBJ7", "WaveEventActions", undefined);

      _export("WaveActionBase", WaveActionBase = class WaveActionBase extends (_crd && EventActionBase === void 0 ? (_reportPossibleCrUseOfEventActionBase({
        error: Error()
      }), EventActionBase) : EventActionBase) {
        onLoad(context) {
          super.onLoad(context);
          this.resetStartValue(context);
          this.resetTargetValue(context);
        }

        onExecute(context, dt) {
          this._elapsedTime += dt;

          if (this._elapsedTime < this._delay) {
            return;
          }

          if (this._elapsedTime >= this._duration) {
            this.onExecuteInternal(context, this._targetValue);
            this._isCompleted = true;
          } else if (this.canLerp()) {
            this.onExecuteInternal(context, this.lerpValue(this._startValue, this._targetValue));
          }
        } // overload EventActionBase methods


        resetStartValue(context) {}

        resetTargetValue(context) {}

        onExecuteInternal(context, value) {}

      });

      _export("WaveAction_SpawnInterval", WaveAction_SpawnInterval = class WaveAction_SpawnInterval extends WaveActionBase {
        resetStartValue(context) {
          this._startValue = context.wave.waveData.spawnInterval.eval();
        }

        onExecuteInternal(context, value) {
          context.wave.waveData.spawnInterval.value = value;
        }

      });

      _export("WaveAction_SpawnAngle", WaveAction_SpawnAngle = class WaveAction_SpawnAngle extends WaveActionBase {
        resetStartValue(context) {
          this._startValue = context.wave.waveData.spawnAngle.eval();
        }

        onExecuteInternal(context, value) {
          context.wave.waveData.spawnAngle.value = value;
        }

      });

      _export("WaveAction_PlaneSpeed", WaveAction_PlaneSpeed = class WaveAction_PlaneSpeed extends WaveActionBase {
        resetStartValue(context) {// this._startValue = context.wave!.waveData.planeSpeed.eval();
        }

        onExecuteInternal(context, value) {// context.wave!.waveData.planeSpeed.value = value;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b2054794b5e86c14a8530bfcdfb7f291335b0167.js.map