{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts"], "names": ["MessageBox", "PopupUI", "ToastUI", "UIMgr", "_tryShowNextMessage", "_showing", "_content", "length", "content", "shift", "onConfirm", "_confirmCallbacks", "onCancel", "_cancelCallbacks", "openUI", "ui", "get", "toClose", "close", "bind", "closeUI", "confirm", "cancelHandler", "show", "push", "toast"], "mappings": ";;;uDAOaA,U;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;AAET;4BAGaH,U,GAAN,MAAMA,UAAN,CAAiB;AAK2C;AAEzB,eAAnBI,mBAAmB,GAAG;AAAA;;AAAA;AACzC,gBAAI,KAAI,CAACC,QAAL,IAAiB,KAAI,CAACC,QAAL,CAAcC,MAAd,KAAyB,CAA9C,EAAiD;AAC/C;AACD;;AACD,YAAA,KAAI,CAACF,QAAL,GAAgB,IAAhB;;AACA,gBAAMG,OAAO,GAAG,KAAI,CAACF,QAAL,CAAcG,KAAd,EAAhB;;AACA,gBAAMC,SAAS,GAAG,KAAI,CAACC,iBAAL,CAAuBF,KAAvB,EAAlB;;AACA,gBAAMG,QAAQ,GAAG,KAAI,CAACC,gBAAL,CAAsBJ,KAAtB,EAAjB,CAPyC,CAQzC;;;AACA,kBAAM;AAAA;AAAA,gCAAMK,MAAN;AAAA;AAAA,oCAAsBN,OAAtB,EAA+BE,SAA/B,EAA0CE,QAA1C,CAAN;AACA,gBAAIG,EAAE,GAAG;AAAA;AAAA,gCAAMC,GAAN;AAAA;AAAA,mCAAT;;AACA,gBAAID,EAAJ,EAAQ;AACNA,cAAAA,EAAE,CAACE,OAAH,GAAa,KAAI,CAACC,KAAL,CAAWC,IAAX,CAAgB,KAAhB,CAAb;AACD;AAbwC;AAc1C;;AAEyB,eAALD,KAAK,GAAG;AAAA;;AAAA;AAC3B,kBAAM;AAAA;AAAA,gCAAME,OAAN;AAAA;AAAA,mCAAN;AACA,YAAA,MAAI,CAACf,QAAL,GAAgB,KAAhB;;AACA,YAAA,MAAI,CAACD,mBAAL;AAH2B;AAI5B,SA3BqB,CA6BtB;;;AACqB,eAAPiB,OAAO,CAACb,OAAD,EAAkBE,SAAlB,EAAuCE,QAAvC,EAA4D;AAC/E,cAAMU,aAAa,GAAGV,QAAQ,KAAK,MAAM,CAAG,CAAd,CAA9B;;AACA,eAAKW,IAAL,CAAUf,OAAV,EAAmBE,SAAnB,EAA8BY,aAA9B;AACD,SAjCqB,CAmCtB;;;AACkB,eAAJC,IAAI,CAACf,OAAD,EAAkBE,SAAlB,EAAwCE,QAAxC,EAAmE;AACnF,eAAKN,QAAL,CAAckB,IAAd,CAAmBhB,OAAnB;;AACA,eAAKG,iBAAL,CAAuBa,IAAvB,CAA4Bd,SAA5B;;AACA,eAAKG,gBAAL,CAAsBW,IAAtB,CAA2BZ,QAA3B;;AACA,eAAKR,mBAAL;AACD;;AAEkB,eAALqB,KAAK,CAACjB,OAAD,EAAkB;AACnC;AAAA;AAAA,8BAAMM,MAAN;AAAA;AAAA,kCAAsBN,OAAtB;AACD;;AA7CqB,O;;AAAXR,MAAAA,U,CAEIK,Q,GAAoB,K;AAFxBL,MAAAA,U,CAGIM,Q,GAAqB,E;AAAI;AAH7BN,MAAAA,U,CAIIW,iB,GAA8C,E;AAAI;AAJtDX,MAAAA,U,CAKIa,gB,GAA6C,E", "sourcesContent": ["import { PopupUI } from \"db://assets/bundles/common/script/ui/common/PopupUI\";\nimport { ToastUI } from \"db://assets/bundles/common/script/ui/common/ToastUI\";\nimport { UIMgr } from \"./UIMgr\";\n\n// 定义回调函数类型\ntype Callback = () => void;\n\nexport class MessageBox {\n\n  private static _showing: boolean = false;\n  private static _content: string[] = []; // 确认回调数组\n  private static _confirmCallbacks: (Callback | undefined)[] = []; // 确认回调数组\n  private static _cancelCallbacks: (Callback | undefined)[] = [];  // 取消回调数组\n\n  private static async _tryShowNextMessage() {\n    if (this._showing || this._content.length === 0) {\n      return;\n    }\n    this._showing = true;\n    const content = this._content.shift();\n    const onConfirm = this._confirmCallbacks.shift();\n    const onCancel = this._cancelCallbacks.shift();\n    // 统一调用 UIMgr\n    await UIMgr.openUI(PopupUI, content, onConfirm, onCancel);\n    let ui = UIMgr.get(PopupUI);\n    if (ui) {\n      ui.toClose = this.close.bind(this);\n    }\n  }\n\n  private static async close() {\n    await UIMgr.closeUI(PopupUI);\n    this._showing = false;\n    this._tryShowNextMessage();\n  }\n\n  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel\n  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {\n    const cancelHandler = onCancel || (() => { });\n    this.show(content, onConfirm, cancelHandler);\n  }\n\n  // 只显示确认按钮，调用这个\n  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {\n    this._content.push(content);\n    this._confirmCallbacks.push(onConfirm);\n    this._cancelCallbacks.push(onCancel);\n    this._tryShowNextMessage();\n  }\n\n  public static toast(content: string) {\n    UIMgr.openUI(ToastUI, content);\n  }\n\n}\n"]}