import { _decorator, Component, Node, Prefab, assetManager, instantiate, Vec2, UITransform, v2 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LayerSplicingMode, LevelDataEvent, LevelDataLayer, LevelDataWave } from 'db://assets/bundles/common/script/leveldata/leveldata';

import { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';
import { LevelEditorWaveUI } from './LevelEditorWaveUI';
import { LevelEditorEventUI } from './LevelEditorEventUI';

const TerrainsNodeName = "terrains";
const ScrollsNodeName = "scrolls";
const DynamicNodeName = "dynamic";
// const WaveNodeName = "waves";
const EventNodeName = "events"

@ccclass('LevelEditorLayerUI')
@executeInEditMode()
export class LevelEditorLayerUI extends Component {
    public terrainsNode: Node|null = null;
    public scrollsNode: Node|null = null;
    public dynamicNode: Node|null = null;
    // public wavesNode: Node|null = null;
    public eventsNode: Node|null = null;

    private _loadScrollNode: boolean = false;

    onLoad(): void {
        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);
        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);
        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);
        // this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);
        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);
    }

    public initByLevelData(data: LevelDataLayer):void {
        console.log("LevelEditorLayerUI initByLevelData");
        if (!data) {
            return;
        }

        if (this.terrainsNode === null) {
            return;
        }
        data.terrains?.forEach((terrain) => {
            assetManager.loadAny({uuid:terrain.uuid}, (err: Error, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                    return
                } 
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);                
            });
        });

        data.dynamics?.forEach((dynamic, index) => {
            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, 'dyna_'+index);
            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);
            dynamic.terrains.forEach((terrain)=>{
                assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                    if (err) {
                        console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                        return
                    } 
                    var dynamicNode = instantiate(prefab);
                    dynaNode!.addChild(dynamicNode);                
                });
            });
        });

        // data.waves?.forEach((wave)=>{
        //     var node = new Node();
        //     var waveUIComp = node.addComponent(LevelEditorWaveUI);
        //     waveUIComp.initByLevelData(wave);
        //     this.wavesNode!.addChild(node);
        // })
        data.events?.forEach((event)=>{
            var node = new Node();
            var eventUIComp = node.addComponent(LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode!.addChild(node);
        })
    }

    public async initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {
        if (!data || this.scrollsNode === null || this._loadScrollNode) {
            return;
        } 

        const loadPromises: Promise<void>[] = [];
        layerData.scrollLayers = [];
        this._loadScrollNode = true;
        data.scrolls?.forEach((scroll) => {
            const scrollLayers = new LevelScrollLayerUI(); 
            scrollLayers.weight = scroll.weight;
            const uuids = scroll.uuids || []; 
            scrollLayers.splicingMode = scroll.splicingMode;
            scrollLayers.splicingOffsetX!.min = scroll.offSetX.min;
            scrollLayers.splicingOffsetX!.max = scroll.offSetX.max;
            scrollLayers.splicingOffsetY!.min = scroll.offSetY.min;
            scrollLayers.splicingOffsetY!.max = scroll.offSetY.max;
            scrollLayers.scrollPrefabs = [];
            uuids.forEach((uuid) => {
                const loadPromise = new Promise<void>((resolve) => {
                    assetManager.loadAny({uuid: uuid}, (err, prefab:Prefab) => {
                        if (err) {
                            resolve();
                            return
                        }           
                        scrollLayers.scrollPrefabs.push(prefab);  
                        
                        console.log("LevelEditorLayerUI initScorllsByLevelData 1--------- 1", layerData.scrollLayers.length);
                        resolve();
                    });
                });
                loadPromises.push(loadPromise);
            });
            layerData.scrollLayers.push(scrollLayers);
        });

        await Promise.all(loadPromises);  
        this._loadScrollNode = false;
        console.log("LevelEditorLayerUI initScorllsByLevelData 1---------", layerData.scrollLayers.length);
        layerData.scrollLayers.forEach((scroll, index) => { 
            const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);
            var totalHeight = data.speed * data.totalTime;
            var posOffsetY = 0;
            var height = 0;
            let prefabIndex = 0; // 当前使用的 prefab 索引
            while (height < totalHeight) {
                // 循环使用 prefab
                const curPrefab = scroll.scrollPrefabs[prefabIndex];
                const child = instantiate(curPrefab);
                const randomOffsetX = Math.random() * (scroll.splicingOffsetX!.max - scroll.splicingOffsetX!.min) + scroll.splicingOffsetX!.min;
                child.setPosition(randomOffsetX, posOffsetY, 0);
                var offY = 0;
                if (scroll.splicingMode === LayerSplicingMode.node_height) {    
                    offY = child.getComponent(UITransform)!.contentSize.height;
                } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {
                    offY = 1334;
                } else if (scroll.splicingMode === LayerSplicingMode.random_height) {
                    offY = Math.max(scroll.splicingOffsetY!.min,scroll.splicingOffsetY!.max) + child.getComponent(UITransform)!.contentSize.height;
                }
                scrollsNode.addChild(child);
                posOffsetY += offY;
                height += offY;
                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
            }
        });
    }

    public fillLevelData(data: LevelDataLayer):void {
        data.terrains = []
        this.terrainsNode!.children.forEach((terrainNode) => {
            data.terrains.push({
                // @ts-ignore
                uuid: terrainNode._prefab.asset._uuid,
                position: new Vec2(terrainNode.position.x, terrainNode.position.y),
                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),
                rotation: terrainNode.rotation.z
            })
        })

        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空
        this.dynamicNode!.children.forEach((dynamicNode, index) => {
            data.dynamics[index].position = v2(dynamicNode.position.x, dynamicNode.position.y);
            data.dynamics[index].scale = v2(dynamicNode.scale.x, dynamicNode.scale.y);
            data.dynamics[index].rotation = dynamicNode.rotation.z;
        });

        // data.waves = []
        // this.wavesNode!.children.forEach((waveNode) => {
        //     var wave = new LevelDataWave()
        //     var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);
        //     waveUIComp!.fillLevelData(wave)
        //     data.waves.push(wave)
        // })
        data.events = []
        this.eventsNode!.children.forEach((eventNode) => {
            var event = new LevelDataEvent()
            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            eventUIComp!.fillLevelData(event)
            data.events.push(event)
        })
    }

    public tick(progress: number, totalTime:number, speed:number):void {
        this.node.setPosition(0, - progress * totalTime * speed, 0);
    }
}