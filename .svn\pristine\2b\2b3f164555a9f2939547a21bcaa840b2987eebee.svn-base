
import { _decorator, Component, Animation, AnimationClip, EventTarget, Vec3, sp, UIOpacity } from 'cc';
import { CharacterState, StateEvent, StateTransition, StateCallback } from './StateDefine';

const { ccclass, property } = _decorator;

@ccclass('StateMachine')
export class StateMachine extends Component {

    @property(sp.Skeleton)
    spine: sp.Skeleton | null = null

    private currentState: CharacterState = CharacterState.NONE;
    private previousState: CharacterState = CharacterState.NONE;
    
    // 当前状态的回调函数
    private currentCallback: StateCallback | null = null;

    // 状态转换规则表
    private transitionTable: StateTransition[] = [
        { from: [CharacterState.NONE], to: CharacterState.IDLE, event: StateEvent.IDLE },
        // 进入状态转换
        { from: [CharacterState.IDLE,CharacterState.NONE], to: CharacterState.ENTER, event: StateEvent.ENTER },
        { from: [CharacterState.ENTER], to: CharacterState.IDLE, event: StateEvent.ANIMATION_END },

        // 移动相关转换
        { from: [CharacterState.IDLE], to: CharacterState.MOVE_LEFT, event: StateEvent.MOVE_LEFT_COMMAND },
        { from: [CharacterState.IDLE], to: CharacterState.MOVE_RIGHT, event: StateEvent.MOVE_RIGHT_COMMAND },
        { from: [CharacterState.MOVE_LEFT, CharacterState.MOVE_RIGHT], to: CharacterState.IDLE, event: StateEvent.MOVE_END },

        // 左右移动互相转换
        { from: [CharacterState.MOVE_LEFT], to: CharacterState.MOVE_RIGHT, event: StateEvent.MOVE_RIGHT_COMMAND },
        { from: [CharacterState.MOVE_RIGHT], to: CharacterState.MOVE_LEFT, event: StateEvent.MOVE_LEFT_COMMAND },

        // 攻击相关转换
        { from: [CharacterState.IDLE, CharacterState.MOVE_LEFT, CharacterState.MOVE_RIGHT], to: CharacterState.ATTACK, event: StateEvent.ATTACK_COMMAND },
        { from: [CharacterState.ATTACK], to: CharacterState.IDLE, event: StateEvent.ANIMATION_END },

        // 闪避相关转换（新增）
        { from: [
            CharacterState.IDLE, 
            CharacterState.MOVE_LEFT,
            CharacterState.MOVE_RIGHT
        ], to: CharacterState.DODGE, event: StateEvent.DODGE_COMMAND },
        { from: [CharacterState.DODGE], to: CharacterState.IDLE, event: StateEvent.ANIMATION_END },

        // 受击转换（可以打断大多数状态）
        { from: [
            CharacterState.IDLE, 
            CharacterState.MOVE_LEFT,
            CharacterState.MOVE_RIGHT,
            CharacterState.ATTACK
        ], to: CharacterState.HIT, event: StateEvent.GET_HIT },
        { from: [CharacterState.HIT], to: CharacterState.IDLE, event: StateEvent.ANIMATION_END },

        // 死亡转换（最高优先级）
        { from: Object.values(CharacterState).filter(v => typeof v === 'number') as CharacterState[], 
          to: CharacterState.DEATH, event: StateEvent.DIE },
    ];
    isAnimPlaying: boolean = false;

    initializeStateMachine(spine: sp.Skeleton) {
        this.spine = spine;
        this.spine.setCompleteListener(this.onAnimationFinished.bind(this));
    }

    /**
     * 改变状态，支持设置回调函数
     * @param newState 新状态
     * @param callback 状态完成后的回调
     */
    public changeState(newState: CharacterState, callback?: StateCallback): boolean {
        // 检查是否允许转换
        if (!this.canTransitionTo(newState)) {
            console.warn(`Cannot transition from ${CharacterState[this.currentState]} to ${CharacterState[newState]}`);
            return false;
        }

        // 记录之前的状态
        this.previousState = this.currentState;

        // 退出当前状态
        this.onExitState(this.currentState);

        // 设置新状态
        this.currentState = newState;
        // console.log(`State changed: ${CharacterState[this.previousState]} -> ${CharacterState[this.currentState]}`);

        // 设置回调函数
        this.currentCallback = callback || null;

        // 进入新状态
        this.onEnterState(newState);

        return true;
    }

    /**
     * 检查是否可以转换到目标状态
     */
    private canTransitionTo(targetState: CharacterState): boolean {
        // 相同的状态不需要转换
        if (this.currentState === targetState) {
            return false;
        }

        // 检查转换表中是否存在允许的转换
        return this.transitionTable.some(transition =>
            transition.to === targetState &&
            transition.from.includes(this.currentState)
        );
    }

    /**
     * 进入状态时的处理
     */
    private onEnterState(state: CharacterState) {
        const animationName = this.getAnimationName(state);
        
        if (animationName && this.spine) {
            // 播放对应的Spine动画
            const loop = this.shouldLoop(state);
            this.spine.setAnimation(0, animationName, loop);
            this.isAnimPlaying = true;
        }

        // console.log('进入:', CharacterState[state]);
        // 状态特定的进入逻辑
        switch (state) {
            case CharacterState.IDLE:
                break;
            case CharacterState.MOVE_LEFT:
            case CharacterState.MOVE_RIGHT:
                break;
            case CharacterState.ATTACK:
                break;
            case CharacterState.HIT:;
                break;
            case CharacterState.DEATH:;
                break;
        }
    }

    /**
     * 判断动画是否应该循环播放
     */
    private shouldLoop(state: CharacterState): boolean {
        // 移动和待机状态循环播放，其他状态不循环
        return [
            CharacterState.IDLE,
            CharacterState.MOVE_LEFT,
            CharacterState.MOVE_RIGHT
        ].includes(state);
    }

    /**
     * 退出状态时的处理
     */
    private onExitState(state: CharacterState) {
        // console.log('退出状态:', CharacterState[state]);
        // 状态特定的退出逻辑
        switch (state) {
            case CharacterState.MOVE_LEFT:
            case CharacterState.MOVE_RIGHT:
                break;
            case CharacterState.ATTACK:
                break;
        }
    }

    /**
     * 根据状态获取动画名称
     */
    private getAnimationName(state: CharacterState): string {
        const animationMap = {
            [CharacterState.ENTER]: 'Entry',
            [CharacterState.IDLE]: 'Idle',
            [CharacterState.MOVE_LEFT]: 'MoveLeft', // 假设有单独的左移动动画
            [CharacterState.MOVE_RIGHT]: 'MoveRight', // 假设有单独的右移动动画
            [CharacterState.ATTACK]: 'Super',
            [CharacterState.HIT]: 'Hurt',
            [CharacterState.DEATH]: 'death',
            [CharacterState.DODGE]: 'Dodge', // 闪避动画
        };

        return animationMap[state as keyof typeof animationMap] || '';
    }

    // 动画播放完成回调
    private onAnimationFinished() {
        this.isAnimPlaying = false;
        
        // 执行回调函数
        if (this.currentCallback) {
            const animationName = this.getAnimationName(this.currentState);
            this.currentCallback(this.currentState, animationName);
            this.currentCallback = null; // 一次性回调，执行后清除
        }

        // 根据当前状态处理动画结束
        switch (this.currentState) {
            case CharacterState.ENTER:
                this.handleEvent(StateEvent.ANIMATION_END);
                break;
            case CharacterState.ATTACK:
                this.handleEvent(StateEvent.ANIMATION_END);
                break;
            case CharacterState.HIT:
                this.handleEvent(StateEvent.ANIMATION_END);
                break;
        }
    }

    // ========== 外部调用的事件处理 ==========
    
    /**
     * 处理状态事件，支持设置回调
     * @param event 状态事件
     * @param callback 回调函数
     */
    public handleEvent(event: StateEvent, callback?: StateCallback): boolean {
        const transition = this.transitionTable.find(t => 
            t.event === event && t.from.includes(this.currentState)
        );

        if (transition) {
            return this.changeState(transition.to, callback);
        }

        return false;
    }

    // ========== 公共方法 ==========

    public getCurrentState(): CharacterState {
        return this.currentState;
    }

    public getPreviousState(): CharacterState {
        return this.previousState;
    }

    public isInState(state: CharacterState): boolean {
        return this.currentState === state;
    }
}