import { SingletonBase } from "../../../../scripts/core/base/SingletonBase";
import FColliderManager from "./collider-system/FColliderManager";
import { BattleManager } from "./manager/BattleManager";
import { BossManager } from "./manager/BossManager";
import { EnemyManager } from "./manager/EnemyManager";
import { GameDataManager } from "./manager/GameDataManager";
import { GamePlaneManager } from "./manager/GamePlaneManager";
import { GameRuleManager } from "./manager/GameRuleManager";
import { HurtEffectManager } from "./manager/HurtEffectManager";
import { MainPlaneManager } from "./manager/MainPlaneManager";
import { StageManager } from "./manager/StageManager";
import WaveManager from "./manager/WaveManager";
import { GameMain } from "./scenes/GameMain";
;

class _GameIns extends SingletonBase<_GameIns> {
    get battleManager() { return BattleManager.getInstance<BattleManager>(BattleManager); }
    get bossManager() { return BossManager.getInstance<BossManager>(BossManager); }
    get enemyManager() { return EnemyManager.getInstance<EnemyManager>(EnemyManager); }
    get gameDataManager() { return GameDataManager.getInstance<GameDataManager>(GameDataManager); }
    get gameRuleManager() { return GameRuleManager.getInstance<GameRuleManager>(GameRuleManager); }
    get hurtEffectManager() { return HurtEffectManager.getInstance<HurtEffectManager>(HurtEffectManager); }
    get mainPlaneManager() { return MainPlaneManager.getInstance<MainPlaneManager>(MainPlaneManager); }
    get gamePlaneManager() { return GamePlaneManager.getInstance<GamePlaneManager>(GamePlaneManager); }
    get stageManager() { return StageManager.getInstance<StageManager>(StageManager); }
    get waveManager() { return WaveManager.getInstance<WaveManager>(WaveManager); }
    get fColliderManager() { return FColliderManager.instance; }

    gameMainUI: GameMain | null = null;
}



export const GameIns: _GameIns = _GameIns.getInstance<_GameIns>(_GameIns);