import { EventActionBase } from "./IEventAction";
import { IEventGroupContext } from "../EventGroup";

export class EmitterActionBase extends EventActionBase {
    // this was intentionally left blank
}

// 修改发射器启用状态
export class EmitterAction_Active extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.isActive.value = value === 1;
    }
}

// 修改发射器初始延迟时间
export class EmitterAction_InitialDelay extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.initialDelay.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.initialDelay.value = value;
    }
}

export class EmitterAction_Prewarm extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.isPreWarm.value ? 1 : 0;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.isPreWarm.value = value === 1;
    }
}

export class EmitterAction_PrewarmDuration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.preWarmDuration.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.preWarmDuration.value = value;
    }
}

// 修改发射器持续时间
export class EmitterAction_Duration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.emitDuration.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.emitDuration.value = value;
    }
}

// 修改发射器已运行时间
export class EmitterAction_ElapsedTime extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.totalElapsedTime.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.totalElapsedTime.value = value;
    }
}

export class EmitterAction_Loop extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.isLoop.value ? 1 : 0;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.isLoop.value = value === 1;
    }
}

export class EmitterAction_LoopInterval extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.loopInterval.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.loopInterval.value = value;
    }
}

export class EmitterAction_EmitInterval extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.emitInterval.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.emitInterval.value = value;
    }
}

export class EmitterAction_PerEmitCount extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.perEmitCount.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.perEmitCount.value = value;
    }
}

export class EmitterAction_PerEmitInterval extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.perEmitInterval.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.perEmitInterval.value = value;
    }
}

export class EmitterAction_PerEmitOffsetX extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.perEmitOffsetX.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.perEmitOffsetX.value = value;
    }
}

export class EmitterAction_Angle extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.angle.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.angle.value = value;
    }
}

export class EmitterAction_Count extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.count.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.count.value = value;
    }
}

// 以下是发射器修改子弹属性的部分
export class EmitterAction_BulletDuration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.duration.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.duration.value = value;
    }
}

export class EmitterAction_BulletDamage extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        // this._startValue = context.emitter!.bulletProp.damage.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        // context.emitter!.bulletProp.damage.value = value;
    }
}

export class EmitterAction_BulletSpeed extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.speed.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.speed.value = value;
    }
}

export class EmitterAction_BulletSpeedAngle extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.speedAngle.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.speedAngle.value = value;
    }
}

export class EmitterAction_BulletAcceleration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.acceleration.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.acceleration.value = value;
    }
}

export class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.accelerationAngle.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.accelerationAngle.value = value;
    }
}

export class EmitterAction_BulletScale extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.scale.value;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.scale.value = value;
    }
}

export class EmitterAction_BulletColorR extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.r;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.r = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletColorG extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.g;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.g = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletColorB extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.b;
    }

    protected executeInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.b = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isFacingMoveDir.value ? 1 : 0;
    }
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isFacingMoveDir.value = value === 1;
    }
}

export class EmitterAction_BulletTrackingTarget extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isTrackingTarget.value ? 1 : 0;
    }
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isTrackingTarget.value = value === 1;
    }
}

export class EmitterAction_BulletDestructive extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isDestructive.value ? 1 : 0;
    }
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isDestructive.value = value === 1;
    }
}

export class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isDestructiveOnHit.value ? 1 : 0;
    }
    protected executeInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isDestructiveOnHit.value = value === 1;
    }
}