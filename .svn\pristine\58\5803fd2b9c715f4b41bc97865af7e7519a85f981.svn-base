[{"task_id": 10010001, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 26, "params": [1001, 1], "desc": "挑战无尽模式/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010002, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090089, 1], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010003, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 3, "params": [1, 100], "desc": "击杀小怪/N个"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 15, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010004, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 18, "params": [1], "desc": "升级装备/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010005, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 10, "params": [3], "desc": "玩家等级达到/N级"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010006, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090090, 2], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010007, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090091, 2], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 10010008, "group_id": 1010, "task_class": 1, "prev_id": 0, "period_type": 1, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090092, 2], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 301, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020001, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 26, "params": [1001, 0, 5], "desc": "挑战无尽模式/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020002, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090089, 1, 10], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020003, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 3, "params": [1, 1000], "desc": "击杀小怪/N个"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 15, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020004, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 18, "params": [5], "desc": "升级装备/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020005, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 10, "params": [10], "desc": "玩家等级达到/N级"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020006, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090093, 1], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020007, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090094, 1], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}, {"task_id": 20020008, "group_id": 1020, "task_class": 2, "prev_id": 0, "period_type": 2, "task_cond": [], "task_goal": {"goal_type": 27, "params": [20090095, 1], "desc": "通过XX玩法/N次"}, "accumulate": false, "reward_id": 10090, "orbit_group_id": 302, "orbit_value": 20, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959", "link_to": ""}]