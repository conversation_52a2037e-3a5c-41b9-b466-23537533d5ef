import { _decorator, Component, director, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D, ResolutionPolicy, view } from 'cc';
import { BottomUI } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { TopUI } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { UIMgr } from '../../../../../scripts/core/base/UIMgr';
import { MBoomUI } from '../../ui/gameui/game/MBoomUI';
import { GameIns } from '../GameIns';
import { BulletSystem } from '../bullet/BulletSystem';
import FCollider from '../collider-system/FCollider';
import { GameConst } from '../const/GameConst';
import { GameEnum } from '../const/GameEnum';
import BattleLayer from '../ui/layer/BattleLayer';

const { ccclass, property } = _decorator;

@ccclass('GameMain')
export class GameMain extends Component {


    @property(BattleLayer)
    BattleLayer: BattleLayer | null = null;

    @property(Node)
    gameEnd: Node | null = null;
    @property(Node)
    CoverBg: Node | null = null;


    protected onLoad(): void {
        GameIns.gameMainUI = this;
        this.CoverBg!.active = true;
        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点

        // 设置分辨率适配策略
        let fitType = ResolutionPolicy.SHOW_ALL
        if (view.getVisibleSize().height / view.getVisibleSize().width * 750 >= 1134){//高度大于 16:9
            fitType = ResolutionPolicy.SHOW_ALL
        }else{//宽屏,比如平板或者电脑
            fitType = ResolutionPolicy.FIXED_HEIGHT
        }
        view.setResolutionPolicy(fitType);
        view.resizeWithBrowserSize(true);

        GameIns.fColliderManager.enable = true;
        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {
            colliderA.entity?.onCollide?.(colliderB);
            colliderB.entity?.onCollide?.(colliderA);
        });

        if (GameConst.ColliderDraw) {
            PhysicsSystem2D.instance.enable = true;
            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;
        }
    }

    start() {
        GameIns.battleManager.startLoading();
        UIMgr.openUI(MBoomUI)
    }

    showGameResult(isSuccess: boolean) {
        this.gameEnd!.active = true;

        let LabelWin = find("LabelWin", this.gameEnd!);
        let LabelFail = find("LabelFail", this.gameEnd!);
        LabelWin!.active = isSuccess;
        LabelFail!.active = !isSuccess;
    }

    async onBtnAgainClicked() {
        this.gameEnd!.active = false;
        GameIns.battleManager.mainReset();
        UIMgr.closeUI(MBoomUI)
        await UIMgr.openUI(HomeUI)
        await UIMgr.openUI(BottomUI)
        await UIMgr.openUI(TopUI)
        director.loadScene("Main");
    }

    /**
     * 每帧更新逻辑
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        // 限制 deltaTime 的最大值
        if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
        }

        switch (GameIns.battleManager.gameType) {
            case GameEnum.GameType.Common:
                GameIns.battleManager.update(deltaTime);
                break;
        }
    }

    lateUpdate(dt: number): void {
        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {
            GameIns.fColliderManager.update(dt);
        }
    }
}


