{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsHurtCell.ts"], "names": ["_decorator", "Component", "Label", "ProgressBar", "Sprite", "tween", "MessageBox", "ccclass", "property", "StatisticsHurtCell", "_expTween", "start", "gap", "exp", "finalExp", "bar", "to", "progress", "onUpdate", "txt", "string", "Math", "round", "update", "deltaTime", "onDestroy", "stop", "setType", "type", "txtType", "show"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AACnDC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;oCAGjBS,kB,WADZF,OAAO,CAAC,oBAAD,C,UAGHC,QAAQ,CAACJ,MAAD,C,UAERI,QAAQ,CAACN,KAAD,C,UAERM,QAAQ,CAACL,WAAD,C,UAERK,QAAQ,CAACN,KAAD,C,UAERM,QAAQ,CAACN,KAAD,C,2BAXb,MACaO,kBADb,SACwCR,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAatCS,SAbsC;AAAA;;AAapB;AAE1BC,QAAAA,KAAK,GAAG;AACJ,cAAMC,GAAG,GAAG,GAAZ;AACA,cAAMC,GAAG,GAAG,GAAZ;AACA,cAAMC,QAAQ,GAAG,KAAjB,CAHI,CAGoB;;AACxB,eAAKJ,SAAL,GAAiBL,KAAK,CAAC,KAAKU,GAAN,CAAL,CACZC,EADY,CACTJ,GADS,EACJ;AAAEK,YAAAA,QAAQ,EAAEJ;AAAZ,WADI,EACe;AACxBK,YAAAA,QAAQ,EAAE,MAAM;AACZ,kBAAI,CAAC,KAAKH,GAAV,EAAe;AACf,mBAAKI,GAAL,CAAUC,MAAV,GAAsBC,IAAI,CAACC,KAAL,CAAWR,QAAQ,GAAG,KAAKC,GAAL,CAASE,QAA/B,CAAtB,SAAkEH,QAAlE;AACH;AAJuB,WADf,EAOZH,KAPY,EAAjB;AAQH;;AAEDY,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKf,SAAT,EAAoB,KAAKA,SAAL,CAAegB,IAAf;AACvB;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAe;AACzB,eAAKC,OAAL,CAAcT,MAAd,GAAuBQ,IAAvB;AACA;AAAA;AAAA,wCAAWE,IAAX,CAAgB,KAAKD,OAAL,CAAcT,MAA9B;AACH;;AAxC6C,O;;;;;iBAGxB,I;;;;;;;iBAEE,I;;;;;;;iBAEE,I;;;;;;;iBAEN,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Label, ProgressBar, Sprite, tween } from 'cc';\r\nimport { MessageBox } from 'db://assets/scripts/core/base/MessageBox';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('StatisticsHurtCell')\r\nexport class StatisticsHurtCell extends Component {\r\n\r\n    @property(Sprite)\r\n    icon: Sprite | null = null;\r\n    @property(Label)\r\n    txtType: Label | null = null;\r\n    @property(ProgressBar)\r\n    bar: ProgressBar | null = null;\r\n    @property(Label)\r\n    txt: Label | null = null;\r\n    @property(Label)\r\n    txt2: Label | null = null;\r\n\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    start() {\r\n        const gap = 0.5;\r\n        const exp = 0.8;\r\n        const finalExp = 10000; // 最终值\r\n        this._expTween = tween(this.bar!)\r\n            .to(gap, { progress: exp }, {\r\n                onUpdate: () => {\r\n                    if (!this.bar) return;\r\n                    this.txt!.string = `${Math.round(finalExp * this.bar.progress)}/${finalExp}`;\r\n                }\r\n            })\r\n            .start();\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    onDestroy() {\r\n        if (this._expTween) this._expTween.stop();\r\n    }\r\n\r\n    public setType(type: string) {\r\n        this.txtType!.string = type;\r\n        MessageBox.show(this.txtType!.string);\r\n    }\r\n}\r\n\r\n\r\n"]}