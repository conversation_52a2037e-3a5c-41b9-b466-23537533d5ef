import { _decorator, CCFloat, CCInteger, CCString, Prefab } from 'cc';
const { ccclass, property} = _decorator;

// @ccclass('LevelEditorWaveParam')
// export class LevelEditorWaveParam {
//     @property(CCString)
//     public name: string = "";
//     @property(CCFloat)
//     public value: number = 0;
// }

@ccclass('LevelEditorWaveGroup') 
export class LevelEditorWaveGroup {
    @property({type: [Prefab], displayName: "波次组"})
    public wavePrefab: Prefab[] = [];
    @property(CCInteger)
    public weight: number = 50;
}