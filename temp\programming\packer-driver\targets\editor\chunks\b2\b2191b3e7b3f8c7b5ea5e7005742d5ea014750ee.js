System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eWaveConditionType, eWaveActionType, ConditionChain, _decorator, WaveEventGroupContext, WaveEventGroup, WaveConditionFactory, WaveActionFactory, _crd, ccclass, property, type;

  function _reportPossibleCrUseOfWaveConditionData(extras) {
    _reporterNs.report("WaveConditionData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroupData(extras) {
    _reporterNs.report("WaveEventGroupData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveActionData(extras) {
    _reporterNs.report("WaveActionData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveConditionType(extras) {
    _reporterNs.report("eWaveConditionType", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveActionType(extras) {
    _reporterNs.report("eWaveActionType", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "db://assets/bundles/common/script/game/bullet/EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfConditionChain(extras) {
    _reporterNs.report("ConditionChain", "db://assets/bundles/common/script/game/bullet/EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "db://assets/bundles/common/script/game/bullet/conditions/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "db://assets/bundles/common/script/game/bullet/actions/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "./Wave", _context.meta, extras);
  }

  _export({
    WaveEventGroupContext: void 0,
    WaveEventGroup: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      eWaveConditionType = _unresolved_2.eWaveConditionType;
      eWaveActionType = _unresolved_2.eWaveActionType;
    }, function (_unresolved_3) {
      ConditionChain = _unresolved_3.ConditionChain;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ffe45opZFdLEahljREt4B8X", "WaveEventGroup", undefined);

      __checkObsolete__(['_decorator', 'CCInteger']);

      ({
        ccclass,
        property,
        type
      } = _decorator);

      _export("WaveEventGroupContext", WaveEventGroupContext = class WaveEventGroupContext {
        constructor() {
          // 继承来的，在波次这里不使用
          this.emitter = null;
          // 继承来的，在波次这里不使用
          this.bullet = null;
          this.playerPlane = null;
          this.wave = null;
        }

        reset() {
          this.emitter = null;
          this.bullet = null;
          this.playerPlane = null;
          this.wave = null;
        }

      }); /// Wave事件组
      /// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData


      _export("WaveEventGroup", WaveEventGroup = class WaveEventGroup {
        constructor(ctx, data) {
          this.data = void 0;
          this.context = void 0;
          this.conditionChain = void 0;
          this.actions = void 0;
          this.context = ctx;
          this.data = data;
          this.conditionChain = this.buildConditionChain(data.conditions);
          this.actions = data.actions.map(actData => {
            const action = WaveActionFactory.create(actData);

            if (action) {
              action.onLoad(this.context);
              return action;
            }

            return null;
          }).filter(act => act !== null);
        }

        canExecute() {
          return this.conditionChain.evaluate(this.context);
        }

        buildConditionChain(conditions) {
          const chain = new (_crd && ConditionChain === void 0 ? (_reportPossibleCrUseOfConditionChain({
            error: Error()
          }), ConditionChain) : ConditionChain)();
          conditions.forEach((condData, index) => {
            const condition = WaveConditionFactory.create(condData);

            if (condition) {
              condition.onLoad(this.context);
              chain.conditions.push(condition);
            }
          });
          return chain;
        }

      });

      WaveConditionFactory = class WaveConditionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eWaveConditionType === void 0 ? (_reportPossibleCrUseOfeWaveConditionType({
              error: Error()
            }), eWaveConditionType) : eWaveConditionType).Spawn_Count:
              return null;

            case (_crd && eWaveConditionType === void 0 ? (_reportPossibleCrUseOfeWaveConditionType({
              error: Error()
            }), eWaveConditionType) : eWaveConditionType).Player_Level:
              return null;

            default:
              break;
          }

          return null;
        }

      };
      WaveActionFactory = class WaveActionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eWaveActionType === void 0 ? (_reportPossibleCrUseOfeWaveActionType({
              error: Error()
            }), eWaveActionType) : eWaveActionType).Spawn_Interval:
              return null;

            case (_crd && eWaveActionType === void 0 ? (_reportPossibleCrUseOfeWaveActionType({
              error: Error()
            }), eWaveActionType) : eWaveActionType).Spawn_Angle:
              return null;

            default:
              break;
          }

          return null;
        }

      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b2191b3e7b3f8c7b5ea5e7005742d5ea014750ee.js.map