
import { _decorator, error, v2, Vec2, Prefab, Enum, CCInteger, CCFloat } from "cc";
import { ExpressionValue } from "./bullet/ExpressionValue";
import { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType, eWrapMode } from "./bullet/EventGroupData";
import { eEasing } from "../bullet/Easing";
const { ccclass, property } = _decorator;

export enum eSpawnOrder {
    Sequential = 0,
    Random = 1,
}

export enum eWaveAngleType {
    FacingMoveDir, FacingPlayer, Fixed, 
}

// 波次完成条件
export enum eWaveCompletion {
    Time, SpawnCount
}

export enum eWaveConditionType {
    Player_Level,       // 玩家等级

    Spawn_Count,        // 当前波次生成数量
}

export enum eWaveActionType {
    Spawn_Interval,      // 出生间隔
    Spawn_Angle,         // 出生角度

    // Plane_Speed,        // 飞机速度
    // Plane_Angle,        // 飞机角度
    // Plane_Accelerate,   // 飞机加速度
    // Plane_AccelerateAngle,  // 飞机加速度角度
    // Plane_OrientationType,  // 飞机朝向类型
    // Plane_TurnSpeed,        // 飞机转向速度
    // Plane_TiltingDistance,  // 飞机振荡距离
    // Plane_TiltingSpeed,     // 飞机振荡速度
}

export enum eWaveConditionTypeCn {
    玩家等级 = eWaveConditionType.Player_Level,
    当前波次生成数量 = eWaveConditionType.Spawn_Count,

}

export enum eWaveActionTypeCn {
    出生间隔 = eWaveActionType.Spawn_Interval,
    出生角度 = eWaveActionType.Spawn_Angle,
    // 飞机速度 = eWaveActionType.Plane_Speed,
    // 飞机角度 = eWaveActionType.Plane_Angle,
    // 飞机加速度 = eWaveActionType.Plane_Accelerate,
    // 飞机加速度角度 = eWaveActionType.Plane_AccelerateAngle,
    // 飞机朝向类型 = eWaveActionType.Plane_OrientationType,
    // 飞机转向速度 = eWaveActionType.Plane_TurnSpeed,
    // 飞机振荡距离 = eWaveActionType.Plane_TiltingDistance,
    // 飞机振荡速度 = eWaveActionType.Plane_TiltingSpeed,
}

// 和发射器的事件组类似
@ccclass("WaveConditionData")
export class WaveConditionData implements IEventConditionData {
    @property({ type: Enum(eConditionOp), displayName: '条件关系' })
    public op: eConditionOp = eConditionOp.And;

    @property({visible:false})
    public type: eWaveConditionType = eWaveConditionType.Player_Level;
    @property({ type: Enum(eWaveConditionTypeCn), displayName: '条件类型' })
    public get typeCn(): eWaveConditionTypeCn { return this.type  as unknown as eWaveConditionTypeCn; }
    public set typeCn(value: eWaveConditionTypeCn) { this.type = value  as unknown as eWaveConditionType; }

    @property({ type: Enum(eCompareOp), displayName: '比较方式' })
    public compareOp: eCompareOp = eCompareOp.Equal;
    
    // 条件值: 例如持续时间、距离
    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
}

@ccclass("WaveActionData")
export class WaveActionData implements IEventActionData {
    @property({ displayName: '行为名称(编辑器下调试用)', editorOnly: true })
    public name: string = "";

    @property({visible:false})
    public type: eWaveActionType = eWaveActionType.Spawn_Interval;
    @property({ type: Enum(eWaveActionTypeCn), displayName: '行为类型' })
    public get typeCn(): eWaveActionTypeCn { return this.type  as unknown as eWaveActionTypeCn; }
    public set typeCn(value: eWaveActionTypeCn) { this.type = value  as unknown as eWaveActionType; }
    
    @property({visible:false})
    public delay : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '延迟时间', tooltip: '事件触发后，延迟多少毫秒开始执行该行为' })
    public get delayStr(): number { return this.delay.value; }
    public set delayStr(value: number) { this.delay.value = value; }

    // 持续时间: 0表示立即执行
    @property({visible:false})
    public duration: ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '持续时间' })
    public get durationStr(): string { return this.duration.raw; }
    public set durationStr(value: string) { this.duration.raw = value; }
    
    @property({visible:false})
    public targetValue: ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
    
    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })
    public targetValueType: eTargetValueType = eTargetValueType.Absolute;
    
    @property({visible:false})
    public transitionDuration : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '变换到目标值所需时间' })
    public get transitionDurationStr(): number { return this.transitionDuration.value; }
    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }
    
    @property({ type: Enum(eWrapMode), displayName: '循环模式' })
    wrapMode: eWrapMode = eWrapMode.Once;

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    public easing: eEasing = eEasing.Linear;
}

@ccclass("WaveEventGroupData")
export class WaveEventGroupData {
    @property({ displayName: '事件组名称' })
    public name: string = "";

    @property({ type: [WaveConditionData], displayName: '条件列表' })
    public conditions: WaveConditionData[] = [];

    @property({ type: [WaveActionData], displayName: '行为列表' })
    public actions: WaveActionData[] = [];
}

@ccclass("SpawnGroup")
export class SpawnGroup {
    @property({type: CCInteger, displayName: "飞机ID"})
    planeID: number = 0;

    @property({type: CCInteger, displayName: "权重"})
    weight: number = 50;

    @property({visible:false, serializable: false})
    selfWeight: number = 0;
}

/**
 * 波次数据：未来代替现有的EnemyWave
 * 所有时间相关的，单位都是毫秒(ms)
 */
@ccclass("WaveData")
export class WaveData {
    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发
    // 因此这里不再配置触发条件
    @property({type: [SpawnGroup], displayName: "出生组"})
    public spawnGroup: SpawnGroup[] = [];

    @property({type: Enum(eSpawnOrder), displayName: "出生顺序"})
    public spawnOrder: eSpawnOrder = eSpawnOrder.Random;

    // @property({visible:false})
    // public count : ExpressionValue = new ExpressionValue('5');
    // @property({displayName: "数量"})
    // public get countStr(): string { return this.count.raw; }
    // public set countStr(value: string) { this.count.raw = value; }
    @property({type: Enum(eWaveCompletion), displayName: "波次完成条件"})
    public waveCompletion: eWaveCompletion = eWaveCompletion.SpawnCount;

    @property({visible:false})
    public waveCompletionParam : ExpressionValue = new ExpressionValue('5');
    @property({displayName: "完成条件参数"})
    public get waveCompletionParamStr(): string { return this.waveCompletionParam.raw; }
    public set waveCompletionParamStr(value: string) { this.waveCompletionParam.raw = value; }

    @property({visible:false})
    public spawnInterval: ExpressionValue = new ExpressionValue('1000');
    @property({displayName: "出生间隔(ms)"})
    public get spawnIntervalStr(): string { return this.spawnInterval.raw; }
    public set spawnIntervalStr(value: string) { this.spawnInterval.raw = value; }

    @property({visible:false})
    public spawnPosX : ExpressionValue = new ExpressionValue('0');
    @property({visible:false})
    public spawnPosY : ExpressionValue = new ExpressionValue('0');
    @property({displayName: "出生位置X"})
    public get spawnPosXStr(): string { return this.spawnPosX.raw; }
    public set spawnPosXStr(value: string) { this.spawnPosX.raw = value; }
    @property({displayName: "出生位置Y"})
    public get spawnPosYStr(): string { return this.spawnPosY.raw; }
    public set spawnPosYStr(value: string) { this.spawnPosY.raw = value; }

    private _spawnPos: Vec2 = new Vec2();
    public get spawnPos(): Vec2 {
        this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());
        return this._spawnPos;
    }

    @property({visible:false})
    public spawnAngle: ExpressionValue = new ExpressionValue('270');
    @property({displayName: "出生角度"})
    public get spawnAngleStr(): string { return this.spawnAngle.raw; }
    public set spawnAngleStr(value: string) { this.spawnAngle.raw = value; }

    // 以下几个属性走怪物的配置里。
    // @property({visible:false})
    // public spawnSpeed: ExpressionValue = new ExpressionValue('500');
    // @property({displayName: "出生速度"})
    // public get spawnSpeedStr(): string { return this.spawnSpeed.raw; }
    // public set spawnSpeedStr(value: string) { this.spawnSpeed.raw = value; }

    // @property({type: Enum(eWaveAngleType), displayName: "单位朝向类型"})
    // public planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;
    // public get isFixedAngleType(): boolean {
    //     return this.planeAngleType == eWaveAngleType.Fixed;
    // }

    // @property({type: CCInteger, displayName: "单位朝向", tooltip: '仅在单位朝向类型为Fixed时有效', 
    //     visible() { 
    //         //@ts-ignore
    //         return this.planeAngleType == eWaveAngleType.Fixed;
    //     }
    // })
    // public planeAngleFixed: number = 0;

    // @property({type: CCInteger, displayName: "单位延迟销毁", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁'})
    // public delayDestroy: number = 5000;

    @property({type: [WaveEventGroupData], displayName: '事件组'})
    public eventGroupData: WaveEventGroupData[] = [];
}