{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventConditions.ts"], "names": ["WaveConditionBase", "WaveCondition_PlayerLevel", "WaveCondition_SpawnCount", "EventConditionBase", "onLoad", "context", "onLoadInternal", "evaluate", "evaluateInternal"], "mappings": ";;;kDAKaA,iB,EAkBAC,yB,EAMAC,wB;;;;;;;;;;;;;;;;;;;;;;;;;;AA7BJC,MAAAA,kB,iBAAAA,kB;;;;;;;mCAKIH,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,oDAAmD;AACtDI,QAAAA,MAAM,CAACC,OAAD,EAAoC;AACtC,gBAAMD,MAAN,CAAaC,OAAb;AACA,eAAKC,cAAL,CAAoBD,OAApB;AACH;;AAESC,QAAAA,cAAc,CAACD,OAAD,EAAuC,CAC9D;;AAEDE,QAAAA,QAAQ,CAACF,OAAD,EAAuC;AAC3C,iBAAO,KAAKG,gBAAL,CAAsBH,OAAtB,CAAP;AACH;;AAESG,QAAAA,gBAAgB,CAACH,OAAD,EAA0C;AAChE,iBAAO,IAAP;AACH;;AAfqD,O;;2CAkB7CJ,yB,GAAN,MAAMA,yBAAN,SAAwCD,iBAAxC,CAA0D;AACnDQ,QAAAA,gBAAgB,CAACH,OAAD,EAA0C;AAChE,iBAAO,KAAP;AACH;;AAH4D,O;;0CAMpDH,wB,GAAN,MAAMA,wBAAN,SAAuCF,iBAAvC,CAAyD;AAClDQ,QAAAA,gBAAgB,CAACH,OAAD,EAA0C;AAChE,iBAAO,KAAP;AACH;;AAH2D,O", "sourcesContent": ["import { EventConditionBase } from \"db://assets/bundles/common/script/game/bullet/conditions/IEventCondition\";\r\nimport { IEventGroupContext, Comparer } from \"db://assets/bundles/common/script/game/bullet/EventGroup\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"db://assets/bundles/common/script/game/data/bullet/EventGroupData\";\r\nimport { WaveEventGroupContext } from \"./WaveEventGroup\";\r\n\r\nexport class WaveConditionBase extends EventConditionBase {\r\n    onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this.onLoadInternal(context as WaveEventGroupContext);\r\n    }\r\n\r\n    protected onLoadInternal(context: WaveEventGroupContext): void {\r\n    }\r\n\r\n    evaluate(context: IEventGroupContext): boolean {\r\n        return this.evaluateInternal(context as WaveEventGroupContext);\r\n    }\r\n\r\n    protected evaluateInternal(context: WaveEventGroupContext): boolean {\r\n        return true;\r\n    }\r\n}\r\n\r\nexport class WaveCondition_PlayerLevel extends WaveConditionBase {\r\n    protected evaluateInternal(context: WaveEventGroupContext): boolean {\r\n        return false;\r\n    }\r\n}\r\n\r\nexport class WaveCondition_SpawnCount extends WaveConditionBase {\r\n    protected evaluateInternal(context: WaveEventGroupContext): boolean {\r\n        return false;\r\n    }\r\n}"]}